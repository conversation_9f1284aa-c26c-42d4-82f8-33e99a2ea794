{"name": "LDPAPM", "version": "1.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@ampproject/remapping": {"version": "2.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@ampproject/remapping/-/remapping-2.2.0.tgz", "integrity": "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}, "dependencies": {"@jridgewell/gen-mapping": {"version": "0.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}}}}, "@babel/code-frame": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/code-frame/-/code-frame-7.18.6.tgz", "integrity": "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==", "dev": true, "requires": {"@babel/highlight": "^7.18.6"}}, "@babel/compat-data": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/compat-data/-/compat-data-7.21.0.tgz", "integrity": "sha512-gMuZsmsgxk/ENC3O/fRw5QY8A9/uxQbbCEypnLIiYYc/qVJtEV7ouxC3EllIIwNzMqAQee5tanFabWsUOutS7g==", "dev": true}, "@babel/core": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/core/-/core-7.21.0.tgz", "integrity": "sha512-PuxUbxcW6ZYe656yL3EAhpy7qXKq0DmYsrJLpbB8XrsCP9Nm+XCg9XFMb5vIDliPD7+U/+M+QJlH17XOcB7eXA==", "dev": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.18.6", "@babel/generator": "^7.21.0", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-module-transforms": "^7.21.0", "@babel/helpers": "^7.21.0", "@babel/parser": "^7.21.0", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.0", "@babel/types": "^7.21.0", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "@babel/eslint-parser": {"version": "7.19.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/eslint-parser/-/eslint-parser-7.19.1.tgz", "integrity": "sha512-AqNf2QWt1rtu2/1rLswy6CDP7H9Oh3mMhk177Y67Rg8d7RD9WfOLLv8CGn6tisFvS2htm86yIe1yLF6I1UDaGQ==", "dev": true, "requires": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.0"}, "dependencies": {"eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "dev": true}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "@babel/generator": {"version": "7.21.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/generator/-/generator-7.21.1.tgz", "integrity": "sha512-1lT45bAYlQhFn/BHivJs43AiW2rg3/UbLyShGfF3C0KmHvO5fSghWd5kBJy30kpRRucGzXStvnnCFniCR2kXAA==", "dev": true, "requires": {"@babel/types": "^7.21.0", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}}, "@babel/helper-annotate-as-pure": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "integrity": "sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz", "integrity": "sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==", "dev": true, "requires": {"@babel/helper-explode-assignable-expression": "^7.18.6", "@babel/types": "^7.18.9"}}, "@babel/helper-compilation-targets": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.7.tgz", "integrity": "sha512-4tGORmfQcrc+bvrjb5y3dG9Mx1IOZjsHqQVUz7XCNHO+iTmqxWnVg3KRygjGmpRLJGdQSKuvFinbIb0CnZwHAQ==", "dev": true, "requires": {"@babel/compat-data": "^7.20.5", "@babel/helper-validator-option": "^7.18.6", "browserslist": "^4.21.3", "lru-cache": "^5.1.1", "semver": "^6.3.0"}, "dependencies": {"lru-cache": {"version": "5.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "requires": {"yallist": "^3.0.2"}}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}, "yallist": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true}}}, "@babel/helper-create-class-features-plugin": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.0.tgz", "integrity": "sha512-Q8wNiMIdwsv5la5SPxNYzzkPnjgC0Sy0i7jLkVOCdllu/xcVNkr3TeZzbHBJrj+XXRqzX5uCyCoV9eu6xUG7KQ==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.21.0", "@babel/helper-member-expression-to-functions": "^7.21.0", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/helper-split-export-declaration": "^7.18.6"}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.0.tgz", "integrity": "sha512-N+LaFW/auRSWdx7SHD/HiARwXQju1vXTW4fKr4u5SgBUTm51OKEjKgj+cs00ggW3kEvNqwErnlwuq7Y3xBe4eg==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "regexpu-core": "^5.3.1"}}, "@babel/helper-define-polyfill-provider": {"version": "0.3.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz", "integrity": "sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.17.7", "@babel/helper-plugin-utils": "^7.16.7", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "@babel/helper-environment-visitor": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "integrity": "sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==", "dev": true}, "@babel/helper-explode-assignable-expression": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz", "integrity": "sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-function-name": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz", "integrity": "sha512-HfK1aMRanKHpxemaY2gqBmL04iAPOPRj7DxtNbiDOrJK+gdwkiNRVpCpUJYbUT+aZyemKN8brqTOxzCaG6ExRg==", "dev": true, "requires": {"@babel/template": "^7.20.7", "@babel/types": "^7.21.0"}}, "@babel/helper-hoist-variables": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "integrity": "sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-member-expression-to-functions": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.21.0.tgz", "integrity": "sha512-Muu8cdZwNN6mRRNG6lAYErJ5X3bRevgYR2O8wN0yn7jJSnGDu6eG59RfT29JHxGUovyfrh6Pj0XzmR7drNVL3Q==", "dev": true, "requires": {"@babel/types": "^7.21.0"}}, "@babel/helper-module-imports": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "integrity": "sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-module-transforms": {"version": "7.21.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-module-transforms/-/helper-module-transforms-7.21.2.tgz", "integrity": "sha512-79yj2AR4U/Oqq/WOV7Lx6hUjau1Zfo4cI+JLAVYeMV5XIlbOhmjEk5ulbTc9fMpmlojzZHkUUxAiK+UKn+hNQQ==", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-validator-identifier": "^7.19.1", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.2", "@babel/types": "^7.21.2"}}, "@babel/helper-optimise-call-expression": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz", "integrity": "sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-plugin-utils": {"version": "7.20.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "integrity": "sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==", "dev": true}, "@babel/helper-remap-async-to-generator": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz", "integrity": "sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-wrap-function": "^7.18.9", "@babel/types": "^7.18.9"}}, "@babel/helper-replace-supers": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-replace-supers/-/helper-replace-supers-7.20.7.tgz", "integrity": "sha512-vujDMtB6LVfNW13jhlCrp48QNslK6JXi7lQG736HVbHz/mbf4Dc7tIRh1Xf5C0rF7BP8iiSxGMCmY6Ci1ven3A==", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.20.7", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/template": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/types": "^7.20.7"}}, "@babel/helper-simple-access": {"version": "7.20.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "integrity": "sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==", "dev": true, "requires": {"@babel/types": "^7.20.2"}}, "@babel/helper-skip-transparent-expression-wrappers": {"version": "7.20.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz", "integrity": "sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==", "dev": true, "requires": {"@babel/types": "^7.20.0"}}, "@babel/helper-split-export-declaration": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "integrity": "sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-string-parser": {"version": "7.19.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "integrity": "sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.19.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "integrity": "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==", "dev": true}, "@babel/helper-validator-option": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz", "integrity": "sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==", "dev": true}, "@babel/helper-wrap-function": {"version": "7.20.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helper-wrap-function/-/helper-wrap-function-7.20.5.tgz", "integrity": "sha512-bYMxIWK5mh+TgXGVqAtnu5Yn1un+v8DDZtqyzKRLUzrh70Eal2O3aZ7aPYiMADO4uKlkzOiRiZ6GX5q3qxvW9Q==", "dev": true, "requires": {"@babel/helper-function-name": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/types": "^7.20.5"}}, "@babel/helpers": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/helpers/-/helpers-7.21.0.tgz", "integrity": "sha512-XXve0CBtOW0pd7MRzzmoyuSj0e3SEzj8pgyFxnTT1NJZL38BD1MK7yYrm8yefRPIDvNNe14xR4FdbHwpInD4rA==", "dev": true, "requires": {"@babel/template": "^7.20.7", "@babel/traverse": "^7.21.0", "@babel/types": "^7.21.0"}}, "@babel/highlight": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/highlight/-/highlight-7.18.6.tgz", "integrity": "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.18.6", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"chalk": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}}}, "@babel/parser": {"version": "7.21.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/parser/-/parser-7.21.2.tgz", "integrity": "sha512-URpaIJQwEkEC2T9Kn+Ai6Xe/02iNaVCuT/PtoRz3GPVJVDpPd7mLo+VddTbhCRU9TXqW5mSrQfXZyi8kDKOVpQ=="}, "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz", "integrity": "sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.20.7.tgz", "integrity": "sha512-sbr9+wNE5aXMBBFBICk01tt7sBf2Oc9ikRFEcem/ZORup9IMUdNhW7/wVLEbbtlWOsEubJet46mHAL2C8+2jKQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.20.7"}}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.7.tgz", "integrity": "sha512-xMbiLsn/8RK7Wq7VeVytytS2L6qE69bXPB10YCmMdDZbKF4okCqY74pI/jJQ/8U0b/F6NrT2+14b8/P9/3AMGA==", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}}, "@babel/plugin-proposal-class-properties": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "integrity": "sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-proposal-class-static-block": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.21.0.tgz", "integrity": "sha512-XP5G9MWNUskFuP30IfFSEFB0Z6HzLIUcjYM4bYOPHXl7eiJ9HFv8tWj6TXTN5QODiEhDZAeI4hLok2iHFFV4hw==", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-class-static-block": "^7.14.5"}}, "@babel/plugin-proposal-decorators": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.0.tgz", "integrity": "sha512-MfgX49uRrFUTL/HvWtmx3zmpyzMMr4MTj3d527MLlr/4RTT9G/ytFFP7qet2uM2Ve03b+BkpWUpK+lRXnQ+v9w==", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/plugin-syntax-decorators": "^7.21.0"}}, "@babel/plugin-proposal-dynamic-import": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz", "integrity": "sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}}, "@babel/plugin-proposal-export-namespace-from": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz", "integrity": "sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}}, "@babel/plugin-proposal-json-strings": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz", "integrity": "sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-json-strings": "^7.8.3"}}, "@babel/plugin-proposal-logical-assignment-operators": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.20.7.tgz", "integrity": "sha512-y7C7cZgpMIjWlKE5T7eJwp+tnRYM89HmRvWM5EQuB5BoHEONjmQ8lSNmBUwOyy/GFRsohJED51YBF79hE1djug==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}}, "@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "integrity": "sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}}, "@babel/plugin-proposal-numeric-separator": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz", "integrity": "sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz", "integrity": "sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg==", "dev": true, "requires": {"@babel/compat-data": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.20.7"}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz", "integrity": "sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}}, "@babel/plugin-proposal-optional-chaining": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz", "integrity": "sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}}, "@babel/plugin-proposal-private-methods": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz", "integrity": "sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0.tgz", "integrity": "sha512-ha4<PERSON><PERSON>hbJjc5MmXBlHec1igel5TJXXLDDRbuJ4+XT2TJcyD9/V1919BA8gMvsdHcNMBy4WBUBiRb3nw/EQUtBw==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz", "integrity": "sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.12.13"}}, "@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-decorators": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.21.0.tgz", "integrity": "sha512-tIoPpGBR8UuM4++ccWN3gifhVvQu7ZizuR1fklhRJrd5ewgbkUS+0KVFeWWxELtn18NTLoW32XV7zyOgIAiz+w==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "integrity": "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "integrity": "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.3"}}, "@babel/plugin-syntax-import-assertions": {"version": "7.20.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz", "integrity": "sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.19.0"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-jsx": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz", "integrity": "sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-typescript": {"version": "7.20.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.20.0.tgz", "integrity": "sha512-rd9TkG+u1CExzS4SM1BlMEhMXwFLKVjOAFFCDx9PbX5ycJWDoWMcwdJH9RhkPu1dOgn5TrxLot/Gx6lWFuAUNQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.19.0"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.20.7.tgz", "integrity": "sha512-3poA5E7dzDomxj9WXWwuD6A5F3kc7VXwIJO+E+J8qtDtS+pXPAhrgEyh+9GBwBgPq1Z+bB+/JD60lp5jsN7JPQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.20.7.tgz", "integrity": "sha512-Uo5gwHPT9vgnSXQxqGtpdufUiWp96gk7yiP4Mp5bm1QMkEmLXBO7PAGYbKoJ6DhAwiNkcHFBol/x5zZZkL/t0Q==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9"}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz", "integrity": "sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-block-scoping": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.0.tgz", "integrity": "sha512-Mdrbunoh9SxwFZapeHVrwFmri16+oYotcZysSzhNIVDwIAb1UV+kvnxULSYq9J3/q5MDG+4X6w8QVgD1zhBXNQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-classes": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.0.tgz", "integrity": "sha512-R<PERSON>hbYTCEUAe6ntPehC4hlslPWosNHDox+vAs4On/mCLRLfoDVHf6hVEd7kuxr1RnHwJmxFfUM3cZiZRmPxJPXQ==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.21.0", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-split-export-declaration": "^7.18.6", "globals": "^11.1.0"}}, "@babel/plugin-transform-computed-properties": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.20.7.tgz", "integrity": "sha512-Lz7MvBK6DTjElHAmfu6bfANzKcxpyNPeYBGEafyA6E5HtRpjpZwU+u7Qrgz/2OR0z+5TvKYbPdphfSaAcZBrYQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/template": "^7.20.7"}}, "@babel/plugin-transform-destructuring": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.20.7.tgz", "integrity": "sha512-Xwg403sRrZb81IVB79ZPqNQME23yhugYVqgTxAhT99h485F4f+GMELFhhOsscDUB7HCswepKeCKLn/GZvUKoBA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-dotall-regex": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz", "integrity": "sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz", "integrity": "sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz", "integrity": "sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==", "dev": true, "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-for-of": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.0.tgz", "integrity": "sha512-LlUYlydgDkKpIY7mcBWvyPPmMcOphEyYA27Ef4xpbh1IiDNLr0kZsos2nf92vz3IccvJI25QUwp86Eo5s6HmBQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-function-name": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz", "integrity": "sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.18.9", "@babel/helper-function-name": "^7.18.9", "@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-literals": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz", "integrity": "sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz", "integrity": "sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-modules-amd": {"version": "7.20.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.20.11.tgz", "integrity": "sha512-NuzCt5IIYOW0O30UvqktzHYR2ud5bOWbY0yaxWZ6G+aFzOMJvrs5YHNikrbdaT15+KNO31nPOy5Fim3ku6Zb5g==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.20.11", "@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.21.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.21.2.tgz", "integrity": "sha512-Cln+Yy04Gxua7iPdj6nOV96smLGjpElir5YwzF0LBPKoPlLDNJePNlrGGaybAJkd0zKRnOVXOgizSqPYMNYkzA==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.21.2", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-simple-access": "^7.20.2"}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.20.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.20.11.tgz", "integrity": "sha512-vVu5g9BPQKSFEmvt2TA4Da5N+QVS66EX21d8uoOihC+OCpUoGvzVsXeqFdtAEfVa5BILAeFt+U7yVmLbQnAJmw==", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.20.11", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-identifier": "^7.19.1"}}, "@babel/plugin-transform-modules-umd": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz", "integrity": "sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.20.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.20.5.tgz", "integrity": "sha512-mOW4tTzi5iTLnw+78iEq3gr8Aoq4WNRGpmSlrogqaiCBoR1HFhpU4JkpQFOHfeYx3ReVIFWOQJS4aZBRvuZ6mA==", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.20.5", "@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-new-target": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz", "integrity": "sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-object-super": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz", "integrity": "sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6"}}, "@babel/plugin-transform-parameters": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.7.tgz", "integrity": "sha512-WiWBIkeHKVOSYPO0pWkxGPfKeWrCJyD3NJ53+Lrp/QMSZbsVPovrVl2aWZ19D/LTVnaDv5Ap7GJ/B2CTOZdrfA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-property-literals": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz", "integrity": "sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-regenerator": {"version": "7.20.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.20.5.tgz", "integrity": "sha512-kW/oO7HPBtntbsahzQ0qSE3tFvkFwnbozz3NWFhLGqH75vLEg+sCGngLlhVkePlCs3Jv0dBBHDzCHxNiFAQKCQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "regenerator-transform": "^0.15.1"}}, "@babel/plugin-transform-reserved-words": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz", "integrity": "sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz", "integrity": "sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-spread": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-spread/-/plugin-transform-spread-7.20.7.tgz", "integrity": "sha512-ewBbHQ+1U/VnH1fxltbJqDeWBU1oNLG8Dj11uIv3xVf7nrQu0bPGe5Rf716r7K5Qz+SqtAOVswoVunoiBtGhxw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz", "integrity": "sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-template-literals": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz", "integrity": "sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz", "integrity": "sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-typescript": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.0.tgz", "integrity": "sha512-xo///XTPp3mDzTtrqXoBlK9eiAYW3wv9JXglcn/u1bi60RW11dEUxIgA8cbnDhutS1zacjMRmAwxE0gMklLnZg==", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}}, "@babel/plugin-transform-unicode-escapes": {"version": "7.18.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz", "integrity": "sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.18.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz", "integrity": "sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/preset-env": {"version": "7.20.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/preset-env/-/preset-env-7.20.2.tgz", "integrity": "sha512-1G0efQEWR1EHkKvKHqbG+IN/QdgwfByUpM5V5QroDzGV2t3S/WXNQd693cHiHTlCFMpr9B6FkPFXDA2lQcKoDg==", "dev": true, "requires": {"@babel/compat-data": "^7.20.1", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.18.6", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.18.9", "@babel/plugin-proposal-async-generator-functions": "^7.20.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-class-static-block": "^7.18.6", "@babel/plugin-proposal-dynamic-import": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-catch-binding": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.18.6", "@babel/plugin-proposal-unicode-property-regex": "^7.18.6", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.18.6", "@babel/plugin-transform-async-to-generator": "^7.18.6", "@babel/plugin-transform-block-scoped-functions": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.20.2", "@babel/plugin-transform-classes": "^7.20.2", "@babel/plugin-transform-computed-properties": "^7.18.9", "@babel/plugin-transform-destructuring": "^7.20.2", "@babel/plugin-transform-dotall-regex": "^7.18.6", "@babel/plugin-transform-duplicate-keys": "^7.18.9", "@babel/plugin-transform-exponentiation-operator": "^7.18.6", "@babel/plugin-transform-for-of": "^7.18.8", "@babel/plugin-transform-function-name": "^7.18.9", "@babel/plugin-transform-literals": "^7.18.9", "@babel/plugin-transform-member-expression-literals": "^7.18.6", "@babel/plugin-transform-modules-amd": "^7.19.6", "@babel/plugin-transform-modules-commonjs": "^7.19.6", "@babel/plugin-transform-modules-systemjs": "^7.19.6", "@babel/plugin-transform-modules-umd": "^7.18.6", "@babel/plugin-transform-named-capturing-groups-regex": "^7.19.1", "@babel/plugin-transform-new-target": "^7.18.6", "@babel/plugin-transform-object-super": "^7.18.6", "@babel/plugin-transform-parameters": "^7.20.1", "@babel/plugin-transform-property-literals": "^7.18.6", "@babel/plugin-transform-regenerator": "^7.18.6", "@babel/plugin-transform-reserved-words": "^7.18.6", "@babel/plugin-transform-shorthand-properties": "^7.18.6", "@babel/plugin-transform-spread": "^7.19.0", "@babel/plugin-transform-sticky-regex": "^7.18.6", "@babel/plugin-transform-template-literals": "^7.18.9", "@babel/plugin-transform-typeof-symbol": "^7.18.9", "@babel/plugin-transform-unicode-escapes": "^7.18.10", "@babel/plugin-transform-unicode-regex": "^7.18.6", "@babel/preset-modules": "^0.1.5", "@babel/types": "^7.20.2", "babel-plugin-polyfill-corejs2": "^0.3.3", "babel-plugin-polyfill-corejs3": "^0.6.0", "babel-plugin-polyfill-regenerator": "^0.4.1", "core-js-compat": "^3.25.1", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "@babel/preset-modules": {"version": "0.1.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/preset-modules/-/preset-modules-0.1.5.tgz", "integrity": "sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}}, "@babel/preset-typescript": {"version": "7.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/preset-typescript/-/preset-typescript-7.21.0.tgz", "integrity": "sha512-myc9mpoVA5m1rF8K8DgLEatOYFDpwC+RkMkjZ0Du6uI62YvDe8uxIEYVs/VCdSJ097nlALiU/yBC7//3nI+hNg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.0"}}, "@babel/regjsgen": {"version": "0.8.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/regjsgen/-/regjsgen-0.8.0.tgz", "integrity": "sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==", "dev": true}, "@babel/runtime": {"version": "7.18.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/runtime/-/runtime-7.18.9.tgz", "integrity": "sha512-lkqXDcvlFT5rvEjiu6+QYO+1GXrEHRo2LOtS7E4GtX5ESIZOgepqsZBVIj6Pv+a6zqsya9VCgiK1KAK4BvJDAw==", "dev": true, "requires": {"regenerator-runtime": "^0.13.4"}}, "@babel/runtime-corejs2": {"version": "7.23.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/runtime-corejs2/-/runtime-corejs2-7.23.6.tgz", "integrity": "sha512-k8QKC2DmBqkwJDOLa4biAZjoCGPQIaAoA1HvHtZ+gR2E9AauudikJOB34h4ETEavN5UG21X0KPdM3IvBRxM0CA==", "requires": {"core-js": "^2.6.12", "regenerator-runtime": "^0.14.0"}, "dependencies": {"regenerator-runtime": {"version": "0.14.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}}}, "@babel/template": {"version": "7.20.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/template/-/template-7.20.7.tgz", "integrity": "sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw==", "dev": true, "requires": {"@babel/code-frame": "^7.18.6", "@babel/parser": "^7.20.7", "@babel/types": "^7.20.7"}}, "@babel/traverse": {"version": "7.21.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/traverse/-/traverse-7.21.2.tgz", "integrity": "sha512-ts5FFU/dSUPS13tv8XiEObDu9K+iagEKME9kAbaP7r0Y9KtZJZ+NGndDvWoRAYNpeWafbpFeki3q9QoMD6gxyw==", "dev": true, "requires": {"@babel/code-frame": "^7.18.6", "@babel/generator": "^7.21.1", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/parser": "^7.21.2", "@babel/types": "^7.21.2", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.21.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/types/-/types-7.21.2.tgz", "integrity": "sha512-3wRZSs7jiFaB8AjxiiD+VqN5DTG2iRvJGQ+qYFrs/654lg6kGTQWIOFjlBo5RaXuAZjBmP3+OQH4dmhqiiyYxw==", "dev": true, "requires": {"@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "to-fast-properties": "^2.0.0"}}, "@better-scroll/core": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/core/-/core-2.5.1.tgz", "integrity": "sha512-koKOuYA55dQ04FJRIVUpMGDr1hbCfWmfX0MGp1hKagkQSWSRpwblqACiwtggVauoj9aaJRJZ9hDsTM4weaavlg==", "requires": {"@better-scroll/shared-utils": "^2.5.1"}}, "@better-scroll/indicators": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/indicators/-/indicators-2.5.1.tgz", "integrity": "sha512-Hk+Y00pR6fTsu6C9HGg1yYZtsu1gAcTgcs4C9aM5h6fQANX/T2YIYrOSjZmdL+js2PTcXJWZS8VM4Xjoi1PbfQ==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/infinity": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/infinity/-/infinity-2.5.1.tgz", "integrity": "sha512-GKHrrasIh0KlGzhASHDo5hEEBJcDFpP4XaZGPH9Ey8+QBH6/O1ykAXS2ixkVAOTkBrv+KgFXoCUr4oN1xWeM+g==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/mouse-wheel": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/mouse-wheel/-/mouse-wheel-2.5.1.tgz", "integrity": "sha512-DGnrirRMY6zMM7xwgx09D/cA9A//3J1/uDkq8iBVEyE5p0sEr/keQpjEfFHGkBRa505BnbBwdbN6f5lugEDSPw==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/movable": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/movable/-/movable-2.5.1.tgz", "integrity": "sha512-8bLPRY15bbK4K5+tjrtdaKsFFKmJx72wRdg+xz3xQGFcTD940HFkJiORSOcz8Ufue7eOJfcmreQJBw6XY+TqTw==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/nested-scroll": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/nested-scroll/-/nested-scroll-2.5.1.tgz", "integrity": "sha512-3cRsARxf9tq1VWBq7YAaET0xGAmgY1ERMmnXDo2gHFrmsJoNOionlpAeHdZvKQp2jG7JrzJ1O27nGCXf40gnkw==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/observe-dom": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/observe-dom/-/observe-dom-2.5.1.tgz", "integrity": "sha512-TCMGFLRfpXBPIwtUV/efliUmfmrhSNI7NXdSyjdWjsLOS7dh3eFkmcom5ERVWMaXVELSmujGXLqobT+dT0C/jg==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/observe-image": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/observe-image/-/observe-image-2.5.1.tgz", "integrity": "sha512-0Lhfj83o8EESwOxr8bfStCzNOokTm3KB7JeyMS8u/xl+3tyTuls9889cyAukYk4Yly1cS49pCGfj2P8YOiwtUg==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/pull-down": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/pull-down/-/pull-down-2.5.1.tgz", "integrity": "sha512-Y6XcGu2NlevPg3k9VBRRFvpmfoTA+rO96JGdog2qKHclIPNXnsVwsIHtZfAm9weE/f9UuC4BnB+VUFRlucfupg==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/pull-up": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/pull-up/-/pull-up-2.5.1.tgz", "integrity": "sha512-1hu3xSMxdB8T391KffpNZ7g93lMwZEHjfb1F1Y4KvIkciDt8nXqkGpqrZF+YwR+EJTgYcWqUO8kgmI6XXu7Pkg==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/scroll-bar": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/scroll-bar/-/scroll-bar-2.5.1.tgz", "integrity": "sha512-i6r60pWG/ztkFK2j5Gj54I0LJb2jGh5TWJNQBoW0gUkp28B+0JvBFTwZn9tF7beZCBorKR7Hvvu4O9A1TJy94Q==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/shared-utils": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/shared-utils/-/shared-utils-2.5.1.tgz", "integrity": "sha512-AplkfSjXVYP9LZiD6JsKgmgQJ/mG4uuLmBuwLz8W5OsYc7AYTfN8kw6GqZ5OwCGoXkVhBGyd8NeC4xwYItp0aw=="}, "@better-scroll/slide": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/slide/-/slide-2.5.1.tgz", "integrity": "sha512-aDOrfsmjAcz6DXN7mDX3tPieAn195R43Yn9e3waI19TIEok/mQlI1a/kb5quqWOoxkiaZQ8xe3vx5ZTj9C+F6Q==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/wheel": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/wheel/-/wheel-2.5.1.tgz", "integrity": "sha512-fYLcEvkh88Z/2L+P5/+SGMunuc+HzAjGOiORIa/x21qb/knO2RFH4A/V1Rt3OIW4QluWzuFnU6jJRPlsQVZ4fg==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@better-scroll/zoom": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@better-scroll/zoom/-/zoom-2.5.1.tgz", "integrity": "sha512-aGvFY5ooeZWS4RcxQLD+pGLpQHQxpPy0sMZV3yadcd2QK53PK9gS4Dp+BYfRv8lZ4/P2LoNEhr6Wq1DN6+uPlA==", "requires": {"@better-scroll/core": "^2.5.1"}}, "@csstools/selector-specificity": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@csstools/selector-specificity/-/selector-specificity-2.1.1.tgz", "integrity": "sha512-jwx+WCqszn53YHOfvFMJJRd/B2GqkCBt+1MJSG6o5/s8+ytHMvDZXsJgUEWLk12UnLd7HYKac4BYU5i/Ron1Cw==", "dev": true}, "@eslint/eslintrc": {"version": "0.4.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@eslint/eslintrc/-/eslintrc-0.4.3.tgz", "integrity": "sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==", "dev": true, "requires": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "globals": "^13.9.0", "ignore": "^4.0.6", "import-fresh": "^3.2.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "strip-json-comments": "^3.1.1"}, "dependencies": {"globals": {"version": "13.20.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globals/-/globals-13.20.0.tgz", "integrity": "sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "type-fest": {"version": "0.20.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true}}}, "@hui/bem-scss": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@hui/bem-scss/-/@hui/bem-scss-1.0.2.tgz", "integrity": "sha1-UA/T2x30cfIXdnF0w3mC84hTjX8=", "requires": {"@hui/shared-utils": "^1.0.2"}}, "@hui/build-tools": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@hui/build-tools/-/@hui/hui-build-tools-1.0.1.tgz", "integrity": "sha1-ge/umTWCL/9kz0v3LoZkXPnckBY=", "requires": {"chalk": "^4.1.0", "classnames": "^2.2.6", "lodash": "^4.17.20", "signale": "^1.4.0", "yargs-parser": "^20.2.4"}, "dependencies": {"yargs-parser": {"version": "20.2.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="}}}, "@hui/scss-utils": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@hui/scss-utils/-/@hui/scss-utils-1.0.5.tgz", "integrity": "sha1-mRTcYElzXupzZfjup8XfiG8Z8hM=", "requires": {"@hui/bem-scss": "^1.0.2-alpha.0", "@hui/shared-utils": "^1.0.2-alpha.0", "normalize-scss": "^7.0.1"}}, "@hui/shared-utils": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@hui/shared-utils/-/@hui/shared-utils-1.0.5.tgz", "integrity": "sha1-Rx/Eipo2zRjxlcXzUAH6iZxen8s=", "requires": {"@hui/build-tools": "^1.0.0", "classnames": "^2.2.6", "lodash": "^4.17.20", "xss": "^1.0.13"}}, "@hui/table": {"version": "1.69.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@hui/table/-/@hui/1.69.0.tgz", "integrity": "sha1-pyCxrpevZLluKziKERUPRkr5Bwo=", "requires": {"@hui/scss-utils": "^1.0.2", "@hui/shared-utils": "^1.0.2", "cross-spawn": "^7.0.3"}}, "@humanwhocodes/config-array": {"version": "0.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@humanwhocodes/config-array/-/config-array-0.5.0.tgz", "integrity": "sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==", "dev": true, "requires": {"@humanwhocodes/object-schema": "^1.2.0", "debug": "^4.1.1", "minimatch": "^3.0.4"}}, "@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "integrity": "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==", "dev": true}, "@interactjs/actions": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/actions/-/actions-1.10.2.tgz", "integrity": "sha512-BHJcW84WCMf/LsKmha/1Yog7aH3+QBXbLvowvZvwYvgjdUIb3xCa1a7FUYXuWAeKNMyKPVjFun+WPce75B+1tA==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/arrange": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/arrange/-/arrange-1.10.2.tgz", "integrity": "sha512-pPLA9o4RWMFN0VfalklOFSRLL4WqqXcD9no4XEuqV00goZPCxLBbMTztaWwnutlRy7evtOhUjUH+pZVsS+dZ4Q=="}, "@interactjs/auto-scroll": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/auto-scroll/-/auto-scroll-1.10.2.tgz", "integrity": "sha512-yYqzOawwvWd1NNnlqZdzrXoOMFafQ2/ws85erpJqdaNMQE221z2uP+QYhFRLQRgYUlTbHFfmjDpzhuJgq4uA8Q==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/auto-start": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/auto-start/-/auto-start-1.10.2.tgz", "integrity": "sha512-nZudj8VzJzz+uEyDHqXwtKpvUYr+Oj1+xBrJEu21CywroHQWM2J4fCIiCgeCo3d5/p/TrzFk5b+YfAWePKiLxA==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/clone": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/clone/-/clone-1.10.2.tgz", "integrity": "sha512-XzA8BRHSCwvysOegZ1kopg+IJF3erh4qzY6DRoZsIJovKAXawoa176E58IAzDbgYPJ2yoaSGT+XyzT2C0wa3pQ=="}, "@interactjs/core": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/core/-/core-1.10.2.tgz", "integrity": "sha512-SA5KRGo+gFJOhBj1Z2dLHhAf0/2nyHNd4SQ460aIQ3jj/QhqbJW6kGzmh7hBa2FzVGgxLhcQu7NZaP4rnDfUNw=="}, "@interactjs/dev-tools": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/dev-tools/-/dev-tools-1.10.2.tgz", "integrity": "sha512-aAd9NgTAGA3yVdFCYcAAYrM4TYQFuVqEvsF+xj+g5SlGyrJ7+GTjPZ2rScOyAsABY4Tz64L2pXvWmXMG87dncA==", "requires": {"@interactjs/interact": "1.10.2", "@interactjs/utils": "1.10.2"}}, "@interactjs/feedback": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/feedback/-/feedback-1.10.2.tgz", "integrity": "sha512-XlcoICGrFeUwwRtDgOpstOOvlU42WZoEg7gJHK3LwF7j0IctPd1+3blXofFlBeVvodle8MvUMepm5CRXz741fA=="}, "@interactjs/inertia": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/inertia/-/inertia-1.10.2.tgz", "integrity": "sha512-ZmN1joN6J36Q6SOp3V0iZOisXZOBMSAUj0STo8wbwCKy7K8IrC9vjUBbO2JM52cT6o7hg5ebHsp5c8FrebSHlg==", "requires": {"@interactjs/interact": "1.10.2", "@interactjs/offset": "1.10.2"}}, "@interactjs/interact": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/interact/-/interact-1.10.2.tgz", "integrity": "sha512-Ms5uVCY9IobVYpQyBnBdkP6Bk6iDY7TkC7GupsdUPUxzAvYSQCTEAGr/1PwxSrSS6dN/8O8TuyUWPbCaylr/JA==", "requires": {"@interactjs/core": "1.10.2", "@interactjs/types": "1.10.2", "@interactjs/utils": "1.10.2"}}, "@interactjs/interactjs": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/interactjs/-/interactjs-1.10.2.tgz", "integrity": "sha512-OwLl70af6lfZOOg/bvWKSNm1DS1nDI72QnzDYljSKfc2D8stqLIGDO1wPY2rhZudUG5q3t50EhmMUQF76yll/g==", "requires": {"@interactjs/actions": "1.10.2", "@interactjs/arrange": "1.10.2", "@interactjs/auto-scroll": "1.10.2", "@interactjs/auto-start": "1.10.2", "@interactjs/clone": "1.10.2", "@interactjs/core": "1.10.2", "@interactjs/dev-tools": "1.10.2", "@interactjs/feedback": "1.10.2", "@interactjs/inertia": "1.10.2", "@interactjs/interact": "1.10.2", "@interactjs/modifiers": "1.10.2", "@interactjs/multi-target": "1.10.2", "@interactjs/offset": "1.10.2", "@interactjs/pointer-events": "1.10.2", "@interactjs/react": "1.10.2", "@interactjs/reflow": "1.10.2", "@interactjs/utils": "1.10.2", "@interactjs/vue": "1.10.2"}}, "@interactjs/modifiers": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/modifiers/-/modifiers-1.10.2.tgz", "integrity": "sha512-3wYEucvZF2NTIslnVIKw5MWhkn9LM42cGCQaC19o3LZeWnbps7NnHJCyQp6zylJrCbwt7f+CSt4Oj2/s0f6XEA==", "requires": {"@interactjs/interact": "1.10.2", "@interactjs/snappers": "1.10.2"}}, "@interactjs/multi-target": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/multi-target/-/multi-target-1.10.2.tgz", "integrity": "sha512-O2GiIqgZBzjAVTOpL8doTnAcM9AtM3+H/Bb+An12wWKtNutVK7JbqUAO2nYueOk55/PP3yDLY9Qdr15RJns3lQ=="}, "@interactjs/offset": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/offset/-/offset-1.10.2.tgz", "integrity": "sha512-xLgQqinFUY7ZqSX9d9on7XRcxvQdHNEAktj2QFwxMsEwrA6zbKROpPVwt8WQ1yBAeJSFjgYGcmCMPW5K41dT0w==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/pointer-events": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/pointer-events/-/pointer-events-1.10.2.tgz", "integrity": "sha512-O8s3N399hkGIzWGlcJVy0LJyDn5YWDh6XKjyowh/QivtlZSWPY8eglmlN2uZX0lmiqUYghbKI4CpQYP/cE0ZDA==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/react": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/react/-/react-1.10.2.tgz", "integrity": "sha512-JXzPdANft+W2vq3SCSzprCwom5UuC8TaiAAhVdt8R+/P6xHbOeAX93XLS5YmDwT8e0Zh9J9jYvz55tkTdwjFZQ=="}, "@interactjs/reflow": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/reflow/-/reflow-1.10.2.tgz", "integrity": "sha512-pc6o6RRhSCYQC4auZexRb7z5FQkdSVev5HzlRfUAjfw4C076qgbcs63ESRKy4YXdSBtUTvARQZxpuWUNGquzJw==", "requires": {"@interactjs/interact": "1.10.2"}}, "@interactjs/snappers": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/snappers/-/snappers-1.10.2.tgz", "integrity": "sha512-wQ41Vn5GRn6VavjIEUtTkd9d5QgdKgC4+CPW0fjKkiQclLBmaic7VibNETO8twN0Jx5e73EoPj9K2nAVHIA0hA=="}, "@interactjs/types": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/types/-/types-1.10.2.tgz", "integrity": "sha512-l0T1bU8OHRv716ztQOYwP+K7b/lA76C0T3r/cdabbUv6CKeAFdFRRFlmNxYOM36SxMGWAiq5VWrN3SeXlB7Fow=="}, "@interactjs/utils": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/utils/-/utils-1.10.2.tgz", "integrity": "sha512-sOr+pu7XGAN4qv+ikajMo3RJygbkbMLegmx0Tv5Qf6e80sF42FjkmHeMGuV7fL98nwat0VmDiWerOFBnKctXow=="}, "@interactjs/vue": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@interactjs/vue/-/vue-1.10.2.tgz", "integrity": "sha512-msLdc42DFsCPQZt6YBGZrw8Ro23kQcNnj+iLz2OUQcOrp/lma7WjorUuAwwfyFna2DevLtiYlMLbT0dpO/cVhg=="}, "@jridgewell/gen-mapping": {"version": "0.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.17", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "integrity": "sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==", "dev": true, "requires": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "@jsep-plugin/assignment": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jsep-plugin/assignment/-/assignment-1.3.0.tgz", "integrity": "sha512-VVgV+CXrhbMI3aSusQyclHkenWSAm95WaiKrMxRFam3JSUiIaQjoMIw2sEs/OX4XifnqeQUN4DYbJjlA8EfktQ=="}, "@jsep-plugin/regex": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@jsep-plugin/regex/-/regex-1.0.4.tgz", "integrity": "sha512-q7qL4Mgjs1vByCaTnDFcBnV9HS7GVPJX5vyVoCgZHNSC9rjwIlmbXG5sUuorR5ndfHAIlJ8pVStxvjXHbNvtUg=="}, "@mrmlnc/readdir-enhanced": {"version": "2.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz", "integrity": "sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g==", "dev": true, "requires": {"call-me-maybe": "^1.0.1", "glob-to-regexp": "^0.3.0"}}, "@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz", "integrity": "sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==", "dev": true, "requires": {"eslint-scope": "5.1.1"}}, "@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@stylelint/postcss-css-in-js": {"version": "0.37.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@stylelint/postcss-css-in-js/-/postcss-css-in-js-0.37.3.tgz", "integrity": "sha512-scLk3cSH1H9KggSniseb2KNAU5D9FWc3H7BxCSAIdtU9OWIyw0zkEZ9qEKHryRM+SExYXRKNb7tOOVNAsQ3iwg==", "dev": true, "requires": {"@babel/core": "^7.17.9"}}, "@stylelint/postcss-markdown": {"version": "0.36.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@stylelint/postcss-markdown/-/postcss-markdown-0.36.2.tgz", "integrity": "sha512-2kGbqUVJUGE8dM+bMzXG/PYUWKkjLIkRLWNh39OaADkiabDRdw8ATFCgbMz5xdIcvwspPAluSL7uY+ZiTWdWmQ==", "dev": true, "requires": {"remark": "^13.0.0", "unist-util-find-all-after": "^3.0.2"}}, "@toolkit-js/iconfig": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@toolkit-js/iconfig/-/iconfig-1.0.0.tgz", "integrity": "sha512-IMtR1sCExkTX0SX0sr8+b3qDO8Z5lW5a9N1D44hK301izaqDWHDCxtAatSdKR9H+wvbNGI2azpq1wRj8QfEQGA==", "dev": true, "requires": {"@babel/core": "^7.14.3", "@babel/eslint-parser": "^7.14.4", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-decorators": "^7.13.5", "@babel/plugin-proposal-private-methods": "^7.13.0", "@babel/preset-env": "^7.12.11", "@babel/preset-typescript": "^7.12.7", "@typescript-eslint/eslint-plugin": "^4.25.0", "@typescript-eslint/parser": "^4.25.0", "@winner-fed/eslint-config-win": "^1.1.0", "@winner-fed/stylelint-config-win": "^0.1.2", "eslint": "^7.27.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-formatter-pretty": "^4.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-unicorn": "^32.0.1", "eslint-plugin-vue": "^7.9.0", "prettier-plugin-style-order": "^0.2.2", "stylelint": "^14.0.1", "stylelint-config-css-modules": "^2.2.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^22.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.3.0", "stylelint-no-unsupported-browser-features": "^5.0.1", "stylelint-order": "^4.1.0", "typescript": "^4.3.2"}, "dependencies": {"@babel/code-frame": {"version": "7.12.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@babel/code-frame/-/code-frame-7.12.11.tgz", "integrity": "sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==", "dev": true, "requires": {"@babel/highlight": "^7.10.4"}}, "balanced-match": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/balanced-match/-/balanced-match-2.0.0.tgz", "integrity": "sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA==", "dev": true}, "escape-string-regexp": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "dev": true}, "eslint": {"version": "7.32.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint/-/eslint-7.32.0.tgz", "integrity": "sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA==", "dev": true, "requires": {"@babel/code-frame": "7.12.11", "@eslint/eslintrc": "^0.4.3", "@humanwhocodes/config-array": "^0.5.0", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "enquirer": "^2.3.5", "escape-string-regexp": "^4.0.0", "eslint-scope": "^5.1.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^2.0.0", "espree": "^7.3.1", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.1.2", "globals": "^13.6.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^6.0.9", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}}, "eslint-plugin-vue": {"version": "7.20.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-plugin-vue/-/eslint-plugin-vue-7.20.0.tgz", "integrity": "sha512-oVNDqzBC9h3GO+NTgWeLMhhGigy6/bQaQbHS+0z7C4YEu/qK/yxHvca/2PTZtGNPsCrHwOTgKMrwu02A9iPBmw==", "dev": true, "requires": {"eslint-utils": "^2.1.0", "natural-compare": "^1.4.0", "semver": "^6.3.0", "vue-eslint-parser": "^7.10.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "dev": true}, "file-entry-cache": {"version": "6.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==", "dev": true, "requires": {"flat-cache": "^3.0.4"}}, "flat-cache": {"version": "3.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/flat-cache/-/flat-cache-3.0.4.tgz", "integrity": "sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==", "dev": true, "requires": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.2.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/flatted/-/flatted-3.2.7.tgz", "integrity": "sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==", "dev": true}, "globals": {"version": "13.20.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globals/-/globals-13.20.0.tgz", "integrity": "sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "hosted-git-info": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "integrity": "sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "html-tags": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/html-tags/-/html-tags-3.2.0.tgz", "integrity": "sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg==", "dev": true}, "known-css-properties": {"version": "0.26.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/known-css-properties/-/known-css-properties-0.26.0.tgz", "integrity": "sha512-5FZRzrZzNTBruuurWpvZnvP9pum+fe0HcK8z/ooo+U+Hmp4vtbyp1/QDsqmufirXy4egGzbaH/y2uCZf+6W5Kg==", "dev": true}, "meow": {"version": "9.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/meow/-/meow-9.0.0.tgz", "integrity": "sha512-+obSblOQmRhcyBt62furQqRAQpNyWXo8BuQ5bN7dG8wmwQ+vwHKp/rCFD4CrTP8CsDQD1sjoZ94K417XEUk8IQ==", "dev": true, "requires": {"@types/minimist": "^1.2.0", "camelcase-keys": "^6.2.2", "decamelize": "^1.2.0", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^3.0.0", "read-pkg-up": "^7.0.1", "redent": "^3.0.0", "trim-newlines": "^3.0.0", "type-fest": "^0.18.0", "yargs-parser": "^20.2.3"}, "dependencies": {"type-fest": {"version": "0.18.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.18.1.tgz", "integrity": "sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==", "dev": true}}}, "normalize-package-data": {"version": "3.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/normalize-package-data/-/normalize-package-data-3.0.3.tgz", "integrity": "sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==", "dev": true, "requires": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}}, "postcss-safe-parser": {"version": "6.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-safe-parser/-/postcss-safe-parser-6.0.0.tgz", "integrity": "sha512-FARHN8pwH+WiS2OPCxJI8FuRJpTVnn6ZNFiqAM2aeW2LwTHWWmWgIyKC6cUo0L8aeKiF/14MNvnpls6R2PBeMQ==", "dev": true}, "resolve-from": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true}, "rimraf": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "dev": true, "requires": {"glob": "^7.1.3"}}, "stylelint": {"version": "14.16.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint/-/stylelint-14.16.1.tgz", "integrity": "sha512-ErlzR/T3hhbV+a925/gbfc3f3Fep9/bnspMiJPorfGEmcBbXdS+oo6LrVtoUZ/w9fqD6o6k7PtUlCOsCRdjX/A==", "dev": true, "requires": {"@csstools/selector-specificity": "^2.0.2", "balanced-match": "^2.0.0", "colord": "^2.9.3", "cosmiconfig": "^7.1.0", "css-functions-list": "^3.1.0", "debug": "^4.3.4", "fast-glob": "^3.2.12", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^6.0.1", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.2.0", "ignore": "^5.2.1", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.26.0", "mathml-tag-names": "^2.1.3", "meow": "^9.0.0", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.19", "postcss-media-query-parser": "^0.2.3", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.11", "postcss-value-parser": "^4.2.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "supports-hyperlinks": "^2.3.0", "svg-tags": "^1.0.0", "table": "^6.8.1", "v8-compile-cache": "^2.3.0", "write-file-atomic": "^4.0.2"}, "dependencies": {"ignore": {"version": "5.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-5.2.4.tgz", "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "dev": true}}}, "type-fest": {"version": "0.20.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true}, "vue-eslint-parser": {"version": "7.11.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-eslint-parser/-/vue-eslint-parser-7.11.0.tgz", "integrity": "sha512-qh3VhDLeh773wjgNTl7ss0VejY9bMMa0GoDG2fQVyDzRFdiU3L7fw74tWZDHNQXdZqxO3EveQroa9ct39D2nqg==", "dev": true, "requires": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^6.3.0"}, "dependencies": {"eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==", "dev": true}, "espree": {"version": "6.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/espree/-/espree-6.2.1.tgz", "integrity": "sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==", "dev": true, "requires": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "write-file-atomic": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "dev": true, "requires": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}}, "yargs-parser": {"version": "20.2.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "dev": true}}}, "@types/eslint": {"version": "7.29.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/eslint/-/eslint-7.29.0.tgz", "integrity": "sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng==", "dev": true, "requires": {"@types/estree": "*", "@types/json-schema": "*"}}, "@types/estree": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/estree/-/estree-1.0.0.tgz", "integrity": "sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==", "dev": true}, "@types/glob": {"version": "7.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/glob/-/glob-7.2.0.tgz", "integrity": "sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==", "dev": true, "requires": {"@types/minimatch": "*", "@types/node": "*"}}, "@types/json-schema": {"version": "7.0.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/json-schema/-/json-schema-7.0.11.tgz", "integrity": "sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ=="}, "@types/json5": {"version": "0.0.29", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/json5/-/json5-0.0.29.tgz", "integrity": "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==", "dev": true}, "@types/mdast": {"version": "3.0.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/mdast/-/mdast-3.0.10.tgz", "integrity": "sha512-W864tg/Osz1+9f4lrGTZpCSO5/z4608eUp19tbozkq2HJK6i3z1kT0H9tlADXuYIb1YYOBByU4Jsqkk75q48qA==", "dev": true, "requires": {"@types/unist": "*"}}, "@types/minimatch": {"version": "5.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/minimatch/-/minimatch-5.1.2.tgz", "integrity": "sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==", "dev": true}, "@types/minimist": {"version": "1.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/minimist/-/minimist-1.2.2.tgz", "integrity": "sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ==", "dev": true}, "@types/node": {"version": "18.14.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/node/-/node-18.14.2.tgz", "integrity": "sha512-1uEQxww3DaghA0RxqHx0O0ppVlo43pJhepY51OxuQIKHpjbnYLA7vcdwioNPzIqmC2u3I/dmylcqjlh0e7AyUA==", "dev": true}, "@types/normalize-package-data": {"version": "2.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz", "integrity": "sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw==", "dev": true}, "@types/parse-json": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==", "dev": true}, "@types/unist": {"version": "2.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/unist/-/unist-2.0.6.tgz", "integrity": "sha512-PBjIUxZHOuj0R15/xuwJYjFi+KZdNFrehocChv4g5hu6aFroHue8m0lBP0POdK2nKzbw0cgV1mws8+V/JAcEkQ==", "dev": true}, "@types/vfile": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/vfile/-/vfile-3.0.2.tgz", "integrity": "sha512-b3nLFGaGkJ9rzOcuXRfHkZMdjsawuDD0ENL9fzTophtBg8FJHSGbH7daXkEpcwy3v7Xol3pAvsmlYyFhR4pqJw==", "dev": true, "requires": {"@types/node": "*", "@types/unist": "*", "@types/vfile-message": "*"}}, "@types/vfile-message": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/vfile-message/-/vfile-message-2.0.0.tgz", "integrity": "sha512-GpTIuDpb9u4zIO165fUy9+fXcULdD8HFRNli04GehoMVbeNq7D6OBnqSmg3lxZnC+UvgUhEWKxdKiwYUkGltIw==", "dev": true, "requires": {"vfile-message": "*"}}, "@typescript-eslint/eslint-plugin": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/eslint-plugin/-/eslint-plugin-4.33.0.tgz", "integrity": "sha512-aINiAxGVdOl1eJyVjaWn/YcVAq4Gi/Yo35qHGCnqbWVz61g39D0h23veY/MA0rFFGfxK7TySg2uwDeNv+JgVpg==", "dev": true, "requires": {"@typescript-eslint/experimental-utils": "4.33.0", "@typescript-eslint/scope-manager": "4.33.0", "debug": "^4.3.1", "functional-red-black-tree": "^1.0.1", "ignore": "^5.1.8", "regexpp": "^3.1.0", "semver": "^7.3.5", "tsutils": "^3.21.0"}, "dependencies": {"ignore": {"version": "5.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-5.2.4.tgz", "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "dev": true}}}, "@typescript-eslint/experimental-utils": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/experimental-utils/-/experimental-utils-4.33.0.tgz", "integrity": "sha512-zeQjOoES5JFjTnAhI5QY7ZviczMzDptls15GFsI6jyUOq0kOf9+WonkhtlIhh0RgHRnqj5gdNxW5j1EvAyYg6Q==", "dev": true, "requires": {"@types/json-schema": "^7.0.7", "@typescript-eslint/scope-manager": "4.33.0", "@typescript-eslint/types": "4.33.0", "@typescript-eslint/typescript-estree": "4.33.0", "eslint-scope": "^5.1.1", "eslint-utils": "^3.0.0"}, "dependencies": {"eslint-utils": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-utils/-/eslint-utils-3.0.0.tgz", "integrity": "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==", "dev": true, "requires": {"eslint-visitor-keys": "^2.0.0"}}, "eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "dev": true}}}, "@typescript-eslint/parser": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/parser/-/parser-4.33.0.tgz", "integrity": "sha512-ZohdsbXadjGBSK0/r+d87X0SBmKzOq4/S5nzK6SBgJspFo9/CUDJ7hjayuze+JK7CZQLDMroqytp7pOcFKTxZA==", "dev": true, "requires": {"@typescript-eslint/scope-manager": "4.33.0", "@typescript-eslint/types": "4.33.0", "@typescript-eslint/typescript-estree": "4.33.0", "debug": "^4.3.1"}}, "@typescript-eslint/scope-manager": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/scope-manager/-/scope-manager-4.33.0.tgz", "integrity": "sha512-5IfJHpgTsTZuONKbODctL4kKuQje/bzBRkwHE8UOZ4f89Zeddg+EGZs8PD8NcN4LdM3ygHWYB3ukPAYjvl/qbQ==", "dev": true, "requires": {"@typescript-eslint/types": "4.33.0", "@typescript-eslint/visitor-keys": "4.33.0"}}, "@typescript-eslint/types": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/types/-/types-4.33.0.tgz", "integrity": "sha512-zKp7CjQzLQImXEpLt2BUw1tvOMPfNoTAfb8l51evhYbOEEzdWyQNmHWWGPR6hwKJDAi+1VXSBmnhL9kyVTTOuQ==", "dev": true}, "@typescript-eslint/typescript-estree": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/typescript-estree/-/typescript-estree-4.33.0.tgz", "integrity": "sha512-rkWRY1MPFzjwnEVHsxGemDzqqddw2QbTJlICPD9p9I9LfsO8fdmfQPOX3uKfUaGRDFJbfrtm/sXhVXN4E+bzCA==", "dev": true, "requires": {"@typescript-eslint/types": "4.33.0", "@typescript-eslint/visitor-keys": "4.33.0", "debug": "^4.3.1", "globby": "^11.0.3", "is-glob": "^4.0.1", "semver": "^7.3.5", "tsutils": "^3.21.0"}}, "@typescript-eslint/visitor-keys": {"version": "4.33.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@typescript-eslint/visitor-keys/-/visitor-keys-4.33.0.tgz", "integrity": "sha512-uqi/2aSz9g2ftcHWf8uLPJA70rUv6yuMW5Bohw+bwcuzaxQIHaKFZCKGoGXIrc9vkTJ3+0txM73K0Hq3d5wgIg==", "dev": true, "requires": {"@typescript-eslint/types": "4.33.0", "eslint-visitor-keys": "^2.0.0"}, "dependencies": {"eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "dev": true}}}, "@vue/babel-helper-vue-jsx-merge-props": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.0.0.tgz", "integrity": "sha512-6tyf5Cqm4m6v7buITuwS+jHzPlIPxbFzEhXR5JGZpbrvOcp1hiQKckd305/3C7C36wFekNTQSxAtgeM0j0yoUw==", "dev": true}, "@vue/babel-plugin-transform-vue-jsx": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz", "integrity": "sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "html-tags": "^2.0.0", "lodash.kebabcase": "^4.1.1", "svg-tags": "^1.0.0"}, "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz", "integrity": "sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==", "dev": true}}}, "@vue/babel-preset-jsx": {"version": "1.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-preset-jsx/-/babel-preset-jsx-1.2.4.tgz", "integrity": "sha512-oRVnmN2a77bYDJzeGSt92AuHXbkIxbf/XXSE3klINnh9AXBmVS1DGa1f0d+dDYpLfsAKElMnqKTQfKn7obcL4w==", "dev": true, "requires": {"@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/babel-plugin-transform-vue-jsx": "^1.2.1", "@vue/babel-sugar-composition-api-inject-h": "^1.2.1", "@vue/babel-sugar-composition-api-render-instance": "^1.2.4", "@vue/babel-sugar-functional-vue": "^1.2.2", "@vue/babel-sugar-inject-h": "^1.2.2", "@vue/babel-sugar-v-model": "^1.2.3", "@vue/babel-sugar-v-on": "^1.2.3"}, "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz", "integrity": "sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==", "dev": true}}}, "@vue/babel-sugar-composition-api-inject-h": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz", "integrity": "sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g==", "dev": true, "requires": {"@babel/plugin-syntax-jsx": "^7.2.0"}}, "@vue/babel-sugar-composition-api-render-instance": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz", "integrity": "sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q==", "dev": true, "requires": {"@babel/plugin-syntax-jsx": "^7.2.0"}}, "@vue/babel-sugar-functional-vue": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz", "integrity": "sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw==", "dev": true, "requires": {"@babel/plugin-syntax-jsx": "^7.2.0"}}, "@vue/babel-sugar-inject-h": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz", "integrity": "sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA==", "dev": true, "requires": {"@babel/plugin-syntax-jsx": "^7.2.0"}}, "@vue/babel-sugar-v-model": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz", "integrity": "sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ==", "dev": true, "requires": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-plugin-transform-vue-jsx": "^1.4.0", "camelcase": "^5.0.0", "html-tags": "^2.0.0", "svg-tags": "^1.0.0"}, "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz", "integrity": "sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==", "dev": true}}}, "@vue/babel-sugar-v-on": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz", "integrity": "sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA==", "dev": true, "requires": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-plugin-transform-vue-jsx": "^1.4.0", "camelcase": "^5.0.0"}}, "@vue/compiler-sfc": {"version": "2.7.14", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@vue/compiler-sfc/-/compiler-sfc-2.7.14.tgz", "integrity": "sha512-aNmNHyLPsw+sVvlQFQ2/8sjNuLtK54TC6cuKnVzAY93ks4ZBrvwQSnkkIh7bsbNhum5hJBS00wSDipQ937f5DA==", "requires": {"@babel/parser": "^7.18.4", "postcss": "^8.4.14", "source-map": "^0.6.1"}}, "@winner-fed/eslint-config-win": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@winner-fed/eslint-config-win/-/eslint-config-win-1.2.0.tgz", "integrity": "sha512-U/8OZt6Z02A696HTrMwbdXsyrHjcgTeScN1ZAuscWD7iU2hieDJRsKPOWHP4J1BNi7Fj4tNJO0l5CUb9x1L6gQ==", "dev": true}, "@winner-fed/stylelint-config-win": {"version": "0.1.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@winner-fed/stylelint-config-win/-/stylelint-config-win-0.1.8.tgz", "integrity": "sha512-UoEu4NsEn1IxXsHTWiXlmevkNDmY3A6WxWbrF74C2agj85v3zcH8mA2M+ktYhvf0hZCILmrY49fM+awXfwUXhg==", "dev": true, "requires": {"stylelint-declaration-block-no-ignored-properties": "^2.3.0", "stylelint-order": "^4.1.0", "stylelint-z-index-value-constraint": "^1.1.0"}}, "JSV": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/JSV/-/JSV-4.0.2.tgz", "integrity": "sha512-ZJ6wx9xaKJ3yFUhq5/sk82PJMuUyLk277I8mQeyDgCTjGdjWJIvPfaU5LIXaMuaN2UO1X3kZH4+lgphublZUHw=="}, "acorn": {"version": "7.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/acorn/-/acorn-7.4.1.tgz", "integrity": "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==", "dev": true}, "acorn-jsx": {"version": "5.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==", "dev": true}, "address": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/address/-/address-1.1.2.tgz", "integrity": "sha512-aT6camzM4xEA54YVJYSqxz1kv4IHnQZRtThJJHhUMRExaU5spC7jX5ugSwTaTgJliIgs4VhZOk7htClvQ/LmRA=="}, "adler-32": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/adler-32/-/adler-32-1.2.0.tgz", "integrity": "sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ==", "requires": {"exit-on-epipe": "~1.0.1", "printj": "~1.1.0"}}, "ajv": {"version": "6.12.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "3.5.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="}, "ansi-colors": {"version": "4.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-colors/-/ansi-colors-4.1.3.tgz", "integrity": "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==", "dev": true}, "ansi-escapes": {"version": "4.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==", "dev": true, "requires": {"type-fest": "^0.21.3"}, "dependencies": {"type-fest": {"version": "0.21.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==", "dev": true}}}, "ansi-regex": {"version": "5.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "ansi-styles": {"version": "3.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "3.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "argparse": {"version": "1.0.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "arr-diff": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA==", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==", "dev": true}, "array-differ": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-differ/-/array-differ-3.0.0.tgz", "integrity": "sha512-THtfYS6KtME/yIAhKjZ2ul7XI96lQGHRputJQHO80LAWQnuGP4iCIN8vdMRboGbIEYBwU33q8Tch1os2+X0kMg==", "dev": true}, "array-find-index": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-find-index/-/array-find-index-1.0.2.tgz", "integrity": "sha512-M1HQyIXcBGtVywBt8WVdim+lrNaK7VHp99Qt5pSNziXznKHViIBbXWtfRTpEFpF/c4FdfxNAsCCwPp5phBYJtw==", "dev": true}, "array-includes": {"version": "3.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-includes/-/array-includes-3.1.6.tgz", "integrity": "sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "get-intrinsic": "^1.1.3", "is-string": "^1.0.7"}}, "array-union": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-union/-/array-union-2.1.0.tgz", "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==", "dev": true}, "array-uniq": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q==", "dev": true}, "array-unique": {"version": "0.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ==", "dev": true}, "array.prototype.flat": {"version": "1.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array.prototype.flat/-/array.prototype.flat-1.3.1.tgz", "integrity": "sha512-roTU0KWIOmJ4DRLmwKd19Otg0/mT3qPNt0Qb3GWW8iObuZXxrjB/pzn0R3hqpRSWg4HCwqx+0vwOnWnvlOyeIA==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}}, "array.prototype.flatmap": {"version": "1.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array.prototype.flatmap/-/array.prototype.flatmap-1.3.1.tgz", "integrity": "sha512-8UGn9O1FDVvMNB0UlLv4voxRMze7+FpHyF5mSMRjWHUMlpoDViniy05870VlxhfgTnLbpuwTzvD76MTtWxB/mQ==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}}, "arrify": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/arrify/-/arrify-2.0.1.tgz", "integrity": "sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==", "dev": true}, "asn1": {"version": "0.2.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "requires": {"safer-buffer": "~2.1.0"}}, "assign-symbols": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==", "dev": true}, "astral-regex": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/astral-regex/-/astral-regex-1.0.0.tgz", "integrity": "sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==", "dev": true}, "async-validator": {"version": "1.12.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/async-validator/-/async-validator-1.12.2.tgz", "integrity": "sha512-57EETfCPFiB7M4QscvQzWSGNsmtkjjzZv318SK1CBlstk+hycV72ocjriMOOM48HjvmoAoJGpJNjC7Z76RlnZA=="}, "asynckit": {"version": "0.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "atob": {"version": "2.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/atob/-/atob-2.1.2.tgz", "integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==", "dev": true}, "autoprefixer": {"version": "9.8.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/autoprefixer/-/autoprefixer-9.8.8.tgz", "integrity": "sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==", "dev": true, "requires": {"browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001109", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "picocolors": "^0.2.1", "postcss": "^7.0.32", "postcss-value-parser": "^4.1.0"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "available-typed-arrays": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "integrity": "sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==", "dev": true}, "babel-eslint": {"version": "10.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/babel-eslint/-/babel-eslint-10.1.0.tgz", "integrity": "sha512-ifWaTHQ0ce+448CYop8AdrQiBsGrnC+bMgfyKFdi6EsPLTAWG+QfyDeM6OH+FmWnKvEq5NnBMLvlBUPKQZoDSg==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.7.0", "@babel/traverse": "^7.7.0", "@babel/types": "^7.7.0", "eslint-visitor-keys": "^1.0.0", "resolve": "^1.12.0"}}, "babel-plugin-polyfill-corejs2": {"version": "0.3.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz", "integrity": "sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==", "dev": true, "requires": {"@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.3.3", "semver": "^6.1.1"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "babel-plugin-polyfill-corejs3": {"version": "0.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz", "integrity": "sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.3.3", "core-js-compat": "^3.25.1"}}, "babel-plugin-polyfill-regenerator": {"version": "0.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz", "integrity": "sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.3.3"}}, "bail": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/bail/-/bail-1.0.5.tgz", "integrity": "sha512-xFbRxM1tahm08yHBP16MMjVUAvDaBMD38zsM9EMAUN61omwLmKlOpB/Zku5QkjZ8TZ4vn53pj+t518cH0S03RQ==", "dev": true}, "balanced-match": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true}, "base": {"version": "0.11.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/base/-/base-0.11.2.tgz", "integrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-1.0.0.tgz", "integrity": "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="}, "batch-processor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/batch-processor/-/batch-processor-1.0.0.tgz", "integrity": "sha512-xoLQD8gmmR32MeuBHgH0Tzd5PuSZx71ZsbhVxOCRbgktZEPe4SQy7s9Z50uPp0F/f7iw2XmkHN2xkgbMfckMDA=="}, "better-scroll": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/better-scroll/-/better-scroll-2.5.1.tgz", "integrity": "sha512-OiF3cQroRfTzf+CRQH2z1G52ZAlNHINI6lCAvDmyFu0o0nRuTaV9F+fmBGIU2BL5p5IplUQ4E7sYa1TLfZarzQ==", "requires": {"@better-scroll/core": "^2.5.1", "@better-scroll/indicators": "^2.5.1", "@better-scroll/infinity": "^2.5.1", "@better-scroll/mouse-wheel": "^2.5.1", "@better-scroll/movable": "^2.5.1", "@better-scroll/nested-scroll": "^2.5.1", "@better-scroll/observe-dom": "^2.5.1", "@better-scroll/observe-image": "^2.5.1", "@better-scroll/pull-down": "^2.5.1", "@better-scroll/pull-up": "^2.5.1", "@better-scroll/scroll-bar": "^2.5.1", "@better-scroll/slide": "^2.5.1", "@better-scroll/wheel": "^2.5.1", "@better-scroll/zoom": "^2.5.1"}}, "big-integer": {"version": "1.6.52", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/big-integer/-/big-integer-1.6.52.tgz", "integrity": "sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg=="}, "big.js": {"version": "5.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/big.js/-/big.js-5.2.2.tgz", "integrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="}, "bignumber.js": {"version": "9.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/bignumber.js/-/bignumber.js-9.0.0.tgz", "integrity": "sha512-t/OYhhJ2SD+YGBQcjY8GzzDHEk9f3nerxjtfa6tlMXfe7frs/WozhvCNoGvpM0P3bNf3Gq5ZRMlGr5f3r4/N8A=="}, "binary-extensions": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw=="}, "boolbase": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="}, "brace-expansion": {"version": "1.1.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.21.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/browserslist/-/browserslist-4.21.5.tgz", "integrity": "sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001449", "electron-to-chromium": "^1.4.284", "node-releases": "^2.0.8", "update-browserslist-db": "^1.0.10"}}, "buffer": {"version": "5.7.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "builtin-modules": {"version": "3.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/builtin-modules/-/builtin-modules-3.3.0.tgz", "integrity": "sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==", "dev": true}, "cache-base": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "call-bind": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "requires": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "dependencies": {"function-bind": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}}}, "call-me-maybe": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/call-me-maybe/-/call-me-maybe-1.0.2.tgz", "integrity": "sha512-HpX65o1Hnr9HH25ojC1YGs7HCQLq0GCOibSaWER0eNpgJ/Z1MZv2mTc7+xh6WOPxbRVcmgbv4hGU+uSQ/2xFZQ==", "dev": true}, "caller-callsite": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/caller-callsite/-/caller-callsite-2.0.0.tgz", "integrity": "sha512-JuG3qI4QOftFsZyOn1qq87fq5grLIyk1JYd5lJmdA+fG7aQ9pA/i3JIJGcO3q0MrRcHlOt1U+ZeHW8Dq9axALQ==", "dev": true, "requires": {"callsites": "^2.0.0"}, "dependencies": {"callsites": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/callsites/-/callsites-2.0.0.tgz", "integrity": "sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ==", "dev": true}}}, "caller-path": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/caller-path/-/caller-path-2.0.0.tgz", "integrity": "sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A==", "dev": true, "requires": {"caller-callsite": "^2.0.0"}}, "callsites": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true}, "camelcase": {"version": "5.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "dev": true}, "camelcase-keys": {"version": "6.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/camelcase-keys/-/camelcase-keys-6.2.2.tgz", "integrity": "sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==", "dev": true, "requires": {"camelcase": "^5.3.1", "map-obj": "^4.0.0", "quick-lru": "^4.0.1"}}, "caniuse-lite": {"version": "1.0.30001547", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/caniuse-lite/-/caniuse-lite-1.0.30001547.tgz", "integrity": "sha512-W7CrtIModMAxobGhz8iXmDfuJiiKg1WADMO/9x7/CLNin5cpSbuBjooyoIUVB5eyCc36QuTVlkVa1iB2S5+/eA==", "dev": true}, "ccount": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ccount/-/ccount-1.1.0.tgz", "integrity": "sha512-vlNK021QdI7PNeiUh/lKkC/mNHHfV0m/Ad5JoI0TYtlBnJAslM/JIkm/tGC88bkLIwO6OQ5uV6ztS6kVAtCDlg==", "dev": true}, "cfb": {"version": "1.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cfb/-/cfb-1.2.2.tgz", "integrity": "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==", "requires": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "dependencies": {"adler-32": {"version": "1.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/adler-32/-/adler-32-1.3.1.tgz", "integrity": "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="}}}, "chalk": {"version": "4.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "requires": {"has-flag": "^4.0.0"}}}}, "character-entities": {"version": "1.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/character-entities/-/character-entities-1.2.4.tgz", "integrity": "sha512-iBMyeEHxfVnIakwOuDXpVkc54HijNgCyQB2w0VfGQThle6NXn50zU6V/u+LDhxHcDUPojn6Kpga3PTAD8W1bQw==", "dev": true}, "character-entities-html4": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/character-entities-html4/-/character-entities-html4-1.1.4.tgz", "integrity": "sha512-HRcDxZuZqMx3/a+qrzxdBKBPUpxWEq9xw2OPZ3a/174ihfrQKVsFhqtthBInFy1zZ9GgZyFXOatNujm8M+El3g==", "dev": true}, "character-entities-legacy": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/character-entities-legacy/-/character-entities-legacy-1.1.4.tgz", "integrity": "sha512-3Xnr+7ZFS1uxeiUDvV02wQ+QDbc55o97tIV5zHScSPJpcLm/r0DFPcoY3tYRp+VZukxuMeKgXYmsXQHO05zQeA==", "dev": true}, "character-reference-invalid": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/character-reference-invalid/-/character-reference-invalid-1.1.4.tgz", "integrity": "sha512-mKKUkUbhPpQlCOfIuZkvSEgktjPFIsZKRRbC6KWVEMvlzblj3i3asQv5ODsrwt0N3pHAEvjP8KTQPHkp0+6jOg==", "dev": true}, "chardet": {"version": "0.7.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chardet/-/chardet-0.7.0.tgz", "integrity": "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==", "dev": true}, "cheerio": {"version": "1.0.0-rc.12", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cheerio/-/cheerio-1.0.0-rc.12.tgz", "integrity": "sha512-VqR8m68vM46BNnuZ5NtnGBKIE/DfN0cRIzg9n40EIq9NOv90ayxLBXA8fXC5gquFRGJSTRqBq25Jt2ECLR431Q==", "requires": {"cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "domutils": "^3.0.1", "htmlparser2": "^8.0.1", "parse5": "^7.0.0", "parse5-htmlparser2-tree-adapter": "^7.0.0"}, "dependencies": {"dom-serializer": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dom-serializer/-/dom-serializer-2.0.0.tgz", "integrity": "sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==", "requires": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}}, "domelementtype": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="}, "domhandler": {"version": "5.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domhandler/-/domhandler-5.0.3.tgz", "integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "requires": {"domelementtype": "^2.3.0"}}, "domutils": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domutils/-/domutils-3.2.2.tgz", "integrity": "sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==", "requires": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}}, "entities": {"version": "4.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="}, "htmlparser2": {"version": "8.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/htmlparser2/-/htmlparser2-8.0.2.tgz", "integrity": "sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==", "requires": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1", "entities": "^4.4.0"}}}}, "cheerio-select": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cheerio-select/-/cheerio-select-2.1.0.tgz", "integrity": "sha512-9v9kG0LvzrlcungtnJtpGNxY+fzECQKhK4EGJX2vByejiMX84MFNQw4UxPJl3bFbTMw+Dfs37XaIkCwTZfLh4g==", "requires": {"boolbase": "^1.0.0", "css-select": "^5.1.0", "css-what": "^6.1.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1"}, "dependencies": {"dom-serializer": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dom-serializer/-/dom-serializer-2.0.0.tgz", "integrity": "sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==", "requires": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}}, "domelementtype": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="}, "domhandler": {"version": "5.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domhandler/-/domhandler-5.0.3.tgz", "integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "requires": {"domelementtype": "^2.3.0"}}, "domutils": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domutils/-/domutils-3.2.2.tgz", "integrity": "sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==", "requires": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}}, "entities": {"version": "4.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="}}}, "chokidar": {"version": "3.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}}, "ci-info": {"version": "3.8.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ci-info/-/ci-info-3.8.0.tgz", "integrity": "sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==", "dev": true}, "class-utils": {"version": "0.3.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha512-Rr7<PERSON>jQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "classnames": {"version": "2.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/classnames/-/classnames-2.5.1.tgz", "integrity": "sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow=="}, "clean-regexp": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/clean-regexp/-/clean-regexp-1.0.0.tgz", "integrity": "sha512-GfisEZEJvzKrmGWkvfhgzcz/BllN1USeqD2V6tg14OAOgaCD2Z/PUEuxnAZ/nPvmaHRG7a8y77p1T/IRQ4D1Hw==", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "cli-cursor": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cli-cursor/-/cli-cursor-3.1.0.tgz", "integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "dev": true, "requires": {"restore-cursor": "^3.1.0"}}, "cli-spinners": {"version": "2.7.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cli-spinners/-/cli-spinners-2.7.0.tgz", "integrity": "sha512-qu3pN8Y3qHNgE2AFweciB1IfMnmZ/fsNTEE+NOFjmGB2F/7rLhnhzppvpCnN4FovtP26k8lHyy9ptEbNwWFLzw==", "dev": true}, "cli-width": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cli-width/-/cli-width-3.0.0.tgz", "integrity": "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==", "dev": true}, "clipboard": {"version": "2.0.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/clipboard/-/clipboard-2.0.11.tgz", "integrity": "sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw==", "requires": {"good-listener": "^1.2.2", "select": "^1.1.2", "tiny-emitter": "^2.0.0"}}, "cliui": {"version": "7.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cliui/-/cliui-7.0.4.tgz", "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "clone": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/clone/-/clone-1.0.4.tgz", "integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "dev": true}, "clone-regexp": {"version": "2.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/clone-regexp/-/clone-regexp-2.2.0.tgz", "integrity": "sha512-beMpP7BOtTipFuW8hrJvREQ2DrRu3BE7by0ZpibtfBA+qfHYvMGTc2Yb1JMYPKg/JUw0CHYvpg796aNTSW9z7Q==", "dev": true, "requires": {"is-regexp": "^2.0.0"}}, "codemirror": {"version": "5.65.14", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/codemirror/-/codemirror-5.65.14.tgz", "integrity": "sha512-VSNugIBDGt0OU9gDjeVr6fNkoFQznrWEUdAApMlXQNbfE8gGO19776D6MwSqF/V/w/sDwonsQ0z7KmmI9guScg=="}, "codepage": {"version": "1.14.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/codepage/-/codepage-1.14.0.tgz", "integrity": "sha512-iz3zJLhlrg37/gYRWgEPkaFTtzmnEv1h+r7NgZum2lFElYQPi0/5bnmuDfODHxfp0INEfnRqyfyeIJDbb7ahRw==", "requires": {"commander": "~2.14.1", "exit-on-epipe": "~1.0.1"}, "dependencies": {"commander": {"version": "2.14.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/commander/-/commander-2.14.1.tgz", "integrity": "sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw=="}}}, "collapse-white-space": {"version": "1.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/collapse-white-space/-/collapse-white-space-1.0.6.tgz", "integrity": "sha512-jEovNnrhMuqyCcjfEJA56v0Xq8SkIoPKDyaHahwo3POf4qcSXqMYuwNcOTzp74vTsR9Tn08z4MxWqAhcekogkQ==", "dev": true}, "collection-visit": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw==", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="}, "colord": {"version": "2.9.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/colord/-/colord-2.9.3.tgz", "integrity": "sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==", "dev": true}, "combined-stream": {"version": "1.0.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.17.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/commander/-/commander-2.17.1.tgz", "integrity": "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg=="}, "component-emitter": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/component-emitter/-/component-emitter-1.3.0.tgz", "integrity": "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==", "dev": true}, "concat-map": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true}, "confusing-browser-globals": {"version": "1.0.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz", "integrity": "sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==", "dev": true}, "convert-source-map": {"version": "1.9.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw==", "dev": true}, "core-js": {"version": "2.6.12", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/core-js/-/core-js-2.6.12.tgz", "integrity": "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ=="}, "core-js-compat": {"version": "3.29.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/core-js-compat/-/core-js-compat-3.29.0.tgz", "integrity": "sha512-ScMn3uZNAFhK2DGoEfErguoiAHhV2Ju+oJo/jK08p7B3f3UhocUrCCkTvnZaiS+edl5nlIoiBXKcwMc6elv4KQ==", "dev": true, "requires": {"browserslist": "^4.21.5"}}, "core-util-is": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="}, "cosmiconfig": {"version": "7.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "integrity": "sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==", "dev": true, "requires": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}}, "crc-32": {"version": "1.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/crc-32/-/crc-32-1.2.2.tgz", "integrity": "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="}, "cross-spawn": {"version": "7.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cross-spawn/-/cross-spawn-7.0.3.tgz", "integrity": "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "css-functions-list": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/css-functions-list/-/css-functions-list-3.1.0.tgz", "integrity": "sha512-/9lCvYZaUbBGvYUgYGFJ4dcYiyqdhSjG7IPVluoV8A1ILjkF7ilmhp1OGUz8n+nmBcu0RNrQAzgD8B6FJbrt2w==", "dev": true}, "css-rule-stream": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/css-rule-stream/-/css-rule-stream-1.1.0.tgz", "integrity": "sha512-qiio/Zkr8I19jh/XuzEkK8OKDQRTrEYaRyIHy4Bwh/tPUe0w8GcQs7r6x24Yc9lT+FbnZFYULxEIXCmaymguUQ==", "dev": true, "requires": {"css-tokenize": "^1.0.1", "duplexer2": "0.0.2", "ldjson-stream": "^1.2.1", "through2": "^0.6.3"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "dev": true}, "through2": {"version": "0.6.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/through2/-/through2-0.6.5.tgz", "integrity": "sha512-RkK/CCESdTKQZHdmKICijdKKsCRVHs5KsLZ6pACAmF/1GPUQhonHSXWNERctxEp7RmvjdNbZTL5z9V7nSCXKcg==", "dev": true, "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "css-select": {"version": "5.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/css-select/-/css-select-5.1.0.tgz", "integrity": "sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==", "requires": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "dependencies": {"dom-serializer": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dom-serializer/-/dom-serializer-2.0.0.tgz", "integrity": "sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==", "requires": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}}, "domelementtype": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="}, "domhandler": {"version": "5.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domhandler/-/domhandler-5.0.3.tgz", "integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "requires": {"domelementtype": "^2.3.0"}}, "domutils": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domutils/-/domutils-3.2.2.tgz", "integrity": "sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==", "requires": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}}, "entities": {"version": "4.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="}}}, "css-tokenize": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/css-tokenize/-/css-tokenize-1.0.1.tgz", "integrity": "sha512-gLmmbJdwH9HLY4bcA17lnZ8GgPwEXRbvxBJGHnkiB6gLhRpTzjkjtMIvz7YORGW/Ptv2oMk8b5g+u7mRD6Dd7A==", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^1.0.33"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "dev": true}, "readable-stream": {"version": "1.1.14", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "dev": true}}}, "css-what": {"version": "6.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/css-what/-/css-what-6.1.0.tgz", "integrity": "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw=="}, "cssesc": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cssesc/-/cssesc-3.0.0.tgz", "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==", "dev": true}, "cssfilter": {"version": "0.0.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cssfilter/-/cssfilter-0.0.10.tgz", "integrity": "sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw=="}, "csstype": {"version": "3.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/csstype/-/csstype-3.1.2.tgz", "integrity": "sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ=="}, "currently-unhandled": {"version": "0.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/currently-unhandled/-/currently-unhandled-0.4.1.tgz", "integrity": "sha512-/fITjgjGU50vjQ4FH6eUoYu+iUoUKIXws2hL15JJpIR+BbTxaXQsMuuyjtNh2WqsSBS5nsaZHFsFecyw5CCAng==", "dev": true, "requires": {"array-find-index": "^1.0.1"}}, "debug": {"version": "4.3.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==", "dev": true}, "decamelize-keys": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/decamelize-keys/-/decamelize-keys-1.1.1.tgz", "integrity": "sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==", "dev": true, "requires": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "dependencies": {"map-obj": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/map-obj/-/map-obj-1.0.1.tgz", "integrity": "sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==", "dev": true}}}, "decode-uri-component": {"version": "0.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "integrity": "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==", "dev": true}, "deep-is": {"version": "0.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==", "dev": true}, "deepmerge": {"version": "1.5.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/deepmerge/-/deepmerge-1.5.2.tgz", "integrity": "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ=="}, "defaults": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/defaults/-/defaults-1.0.4.tgz", "integrity": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==", "dev": true, "requires": {"clone": "^1.0.2"}}, "define-properties": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-properties/-/define-properties-1.2.0.tgz", "integrity": "sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==", "dev": true, "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "define-property": {"version": "2.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-2.0.2.tgz", "integrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "delegate": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/delegate/-/delegate-3.2.0.tgz", "integrity": "sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw=="}, "diff-match-patch": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/diff-match-patch/-/diff-match-patch-1.0.5.tgz", "integrity": "sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw=="}, "dir-glob": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==", "dev": true, "requires": {"path-type": "^4.0.0"}}, "doctrine": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==", "dev": true, "requires": {"esutils": "^2.0.2"}}, "doiuse": {"version": "4.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/doiuse/-/doiuse-4.4.1.tgz", "integrity": "sha512-TUpr1/YNg20IB09tZmwGCTsTQoxj8jUld/hUZprZMj8vj0VpAJySXEWCr8WMvqvgzk0/kG/FxeSMGKode4UjPg==", "dev": true, "requires": {"browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001179", "css-rule-stream": "^1.1.0", "duplexer2": "0.0.2", "ldjson-stream": "^1.2.1", "multimatch": "^5.0.0", "postcss": "^8.2.4", "source-map": "^0.7.3", "through2": "^4.0.2", "yargs": "^16.2.0"}, "dependencies": {"source-map": {"version": "0.7.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-map/-/source-map-0.7.4.tgz", "integrity": "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==", "dev": true}}}, "dom-serializer": {"version": "0.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dom-serializer/-/dom-serializer-0.2.2.tgz", "integrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==", "dev": true, "requires": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "dependencies": {"domelementtype": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==", "dev": true}, "entities": {"version": "2.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/entities/-/entities-2.2.0.tgz", "integrity": "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==", "dev": true}}}, "dom7": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dom7/-/dom7-3.0.0.tgz", "integrity": "sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g==", "requires": {"ssr-window": "^3.0.0-alpha.1"}}, "domelementtype": {"version": "1.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domelementtype/-/domelementtype-1.3.1.tgz", "integrity": "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==", "dev": true}, "domhandler": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domhandler/-/domhandler-2.4.2.tgz", "integrity": "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==", "dev": true, "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.7.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domutils/-/domutils-1.7.0.tgz", "integrity": "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==", "dev": true, "requires": {"dom-serializer": "0", "domelementtype": "1"}}, "dot-prop": {"version": "5.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dot-prop/-/dot-prop-5.3.0.tgz", "integrity": "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==", "dev": true, "requires": {"is-obj": "^2.0.0"}}, "dunder-proto": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "requires": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "dependencies": {"gopd": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="}}}, "duplexer2": {"version": "0.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/duplexer2/-/duplexer2-0.0.2.tgz", "integrity": "sha512-+AWBwjGadtksxjOQSFDhPNQbed7icNXApT4+2BNpsXzcCBiInq2H9XW0O8sfHFaPmnQRs7cg/P0fAr2IWQSW0g==", "dev": true, "requires": {"readable-stream": "~1.1.9"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "dev": true}, "readable-stream": {"version": "1.1.14", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "dev": true}}}, "echarts": {"version": "5.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/echarts/-/echarts-5.4.1.tgz", "integrity": "sha512-9ltS3M2JB0w2EhcYjCdmtrJ+6haZcW6acBolMGIuf01Hql1yrIV01L1aRj7jsaaIULJslEP9Z3vKlEmnJaWJVQ==", "requires": {"tslib": "2.3.0", "zrender": "5.4.1"}}, "electron-to-chromium": {"version": "1.4.311", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/electron-to-chromium/-/electron-to-chromium-1.4.311.tgz", "integrity": "sha512-RoDlZufvrtr2Nx3Yx5MB8jX3aHIxm8nRWPJm3yVvyHmyKaRvn90RjzB6hNnt0AkhS3IInJdyRfQb4mWhPvUjVw==", "dev": true}, "element-resize-detector": {"version": "1.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/element-resize-detector/-/element-resize-detector-1.2.4.tgz", "integrity": "sha512-Fl5Ftk6WwXE0wqCgNoseKWndjzZlDCwuPTcoVZfCP9R3EHQF8qUtr3YUPNETegRBOKqQKPW3n4kiIWngGi8tKg==", "requires": {"batch-processor": "1.0.0"}}, "emitter-component": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/emitter-component/-/emitter-component-1.1.1.tgz", "integrity": "sha512-G+mpdiAySMuB7kesVRLuyvYRqDmshB7ReKEVuyBPkzQlmiDiLrt7hHHIy4Aff552bgknVN7B2/d3lzhGO5dvpQ=="}, "emoji-regex": {"version": "8.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "emojis-list": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/emojis-list/-/emojis-list-3.0.0.tgz", "integrity": "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="}, "encoding": {"version": "0.1.13", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "requires": {"iconv-lite": "^0.6.2"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}}}, "enquirer": {"version": "2.3.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/enquirer/-/enquirer-2.3.6.tgz", "integrity": "sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg==", "dev": true, "requires": {"ansi-colors": "^4.1.1"}}, "entities": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/entities/-/entities-1.1.2.tgz", "integrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==", "dev": true}, "error-ex": {"version": "1.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "requires": {"is-arrayish": "^0.2.1"}}, "es-abstract": {"version": "1.21.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-abstract/-/es-abstract-1.21.1.tgz", "integrity": "sha512-QudMsPOz86xYz/1dG1OuGBKOELjCh99IIWHLzy5znUB6j8xG2yMA7bfTV86VSqKF+Y/H08vQPR+9jyXpuC6hfg==", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.3", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.4", "is-array-buffer": "^3.0.1", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "safe-regex-test": "^1.0.0", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.9"}}, "es-define-property": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="}, "es-errors": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="}, "es-object-atoms": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "requires": {"es-errors": "^1.3.0"}}, "es-set-tostringtag": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz", "integrity": "sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==", "dev": true, "requires": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}}, "es-shim-unscopables": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz", "integrity": "sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==", "dev": true, "requires": {"has": "^1.0.3"}}, "es-to-primitive": {"version": "1.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "integrity": "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==", "dev": true, "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escalade": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="}, "eslint": {"version": "7.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint/-/eslint-7.2.0.tgz", "integrity": "sha512-B3BtEyaDKC5MlfDa2Ha8/D6DsS4fju95zs0hjS3HdGazw+LNayai38A25qMppK37wWGWNYSPOR6oYzlz5MHsRQ==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^5.1.0", "eslint-utils": "^2.0.0", "eslint-visitor-keys": "^1.2.0", "espree": "^7.1.0", "esquery": "^1.2.0", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.0.0", "globals": "^12.1.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^7.0.0", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash": "^4.17.14", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^5.2.3", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "dependencies": {"ansi-regex": {"version": "4.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-regex/-/ansi-regex-4.1.1.tgz", "integrity": "sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==", "dev": true}, "emoji-regex": {"version": "7.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/emoji-regex/-/emoji-regex-7.0.3.tgz", "integrity": "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==", "dev": true}, "globals": {"version": "12.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globals/-/globals-12.4.0.tgz", "integrity": "sha512-BWICuzzDvDoH54NHKCseDanAhE3CeDorgDL5MT6LMXXj2WCnd9UC2szdk4AWLfjdgNBCXLUanXYcpBBKOSWGwg==", "dev": true, "requires": {"type-fest": "^0.8.1"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==", "dev": true}, "string-width": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string-width/-/string-width-3.1.0.tgz", "integrity": "sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "dependencies": {"strip-ansi": {"version": "5.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}}}, "table": {"version": "5.4.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/table/-/table-5.4.6.tgz", "integrity": "sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==", "dev": true, "requires": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}}}}, "eslint-config-airbnb-base": {"version": "14.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-config-airbnb-base/-/eslint-config-airbnb-base-14.2.1.tgz", "integrity": "sha512-GOrQyDtVEc1Xy20U7vsB2yAoB4nBlfH5HZJeatRXHleO+OS5Ot+MWij4Dpltw4/DyIkqUfqz1epfhVR5XWWQPA==", "dev": true, "requires": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.2"}}, "eslint-config-prettier": {"version": "8.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-config-prettier/-/eslint-config-prettier-8.6.0.tgz", "integrity": "sha512-bAF0eLpLVqP5oEVUFKpMA+NnRFICwn9X8B5jrR9FcqnYBuPbqWEjTEspPWMj5ye6czoSLDweCzSo3Ko7gGrZaA==", "dev": true}, "eslint-formatter-pretty": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-formatter-pretty/-/eslint-formatter-pretty-4.1.0.tgz", "integrity": "sha512-IsUTtGxF1hrH6lMWiSl1WbGaiP01eT6kzywdY1U+zLc0MP+nwEnUiS9UI8IaOTUhTeQJLlCEWIbXINBH4YJbBQ==", "dev": true, "requires": {"@types/eslint": "^7.2.13", "ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "eslint-rule-docs": "^1.1.5", "log-symbols": "^4.0.0", "plur": "^4.0.0", "string-width": "^4.2.0", "supports-hyperlinks": "^2.0.0"}, "dependencies": {"log-symbols": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "dev": true, "requires": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}}}}, "eslint-import-resolver-node": {"version": "0.3.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.7.tgz", "integrity": "sha512-gozW2blMLJCeFpBwugLTGyvVjNoeo1knonXAcatC6bjPBZitotxdWf7Gimr25N4c0AAOo4eOUfaG82IJPDpqCA==", "dev": true, "requires": {"debug": "^3.2.7", "is-core-module": "^2.11.0", "resolve": "^1.22.1"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "eslint-module-utils": {"version": "2.7.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-module-utils/-/eslint-module-utils-2.7.4.tgz", "integrity": "sha512-j4GT+rqzCoRKHwURX7pddtIPGySnX9Si/cgMI5ztrcqOPtk5dDEeZ34CQVPphnqkJytlc97Vuk05Um2mJ3gEQA==", "dev": true, "requires": {"debug": "^3.2.7"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "eslint-plugin-eslint-comments": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-plugin-eslint-comments/-/eslint-plugin-eslint-comments-3.2.0.tgz", "integrity": "sha512-0jkOl0hfojIHHmEHgmNdqv4fmh7300NdpA9FFpF7zaoLvB/QeXOGNLIo86oAveJFrfB1p05kC8hpEMHM8DwWVQ==", "dev": true, "requires": {"escape-string-regexp": "^1.0.5", "ignore": "^5.0.5"}, "dependencies": {"ignore": {"version": "5.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-5.2.4.tgz", "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "dev": true}}}, "eslint-plugin-import": {"version": "2.27.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-plugin-import/-/eslint-plugin-import-2.27.5.tgz", "integrity": "sha512-LmEt3GVofgiGuiE+ORpnvP+kAm3h6MLZJ4Q5HCyHADofsb4VzXFsRiWj3c0OFiV+3DWFh0qg3v9gcPlfc3zRow==", "dev": true, "requires": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "array.prototype.flatmap": "^1.3.1", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.7", "eslint-module-utils": "^2.7.4", "has": "^1.0.3", "is-core-module": "^2.11.0", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.values": "^1.1.6", "resolve": "^1.22.1", "semver": "^6.3.0", "tsconfig-paths": "^3.14.1"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "requires": {"ms": "^2.1.1"}}, "doctrine": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==", "dev": true, "requires": {"esutils": "^2.0.2"}}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true}}}, "eslint-plugin-jest": {"version": "24.7.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-plugin-jest/-/eslint-plugin-jest-24.7.0.tgz", "integrity": "sha512-wUxdF2bAZiYSKBclsUMrYHH6WxiBreNjyDxbRv345TIvPeoCEgPNEn3Sa+ZrSqsf1Dl9SqqSREXMHExlMMu1DA==", "dev": true, "requires": {"@typescript-eslint/experimental-utils": "^4.0.1"}}, "eslint-plugin-unicorn": {"version": "32.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-plugin-unicorn/-/eslint-plugin-unicorn-32.0.1.tgz", "integrity": "sha512-<PERSON>Z9utnXtOJjnoDkpm+nQsONUUmyRR0WD6PGROSdQRRW3LRmgK/ZP8wxjW+Ai+2uolKTtuJzLx2mvbIeIoLqpg==", "dev": true, "requires": {"ci-info": "^3.1.1", "clean-regexp": "^1.0.0", "eslint-template-visitor": "^2.3.2", "eslint-utils": "^2.1.0", "import-modules": "^2.1.0", "is-builtin-module": "^3.1.0", "lodash": "^4.17.21", "pluralize": "^8.0.0", "read-pkg-up": "^7.0.1", "regexp-tree": "^0.1.23", "reserved-words": "^0.1.2", "safe-regex": "^2.1.1", "semver": "^7.3.5"}}, "eslint-plugin-vue": {"version": "6.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-plugin-vue/-/eslint-plugin-vue-6.2.2.tgz", "integrity": "sha512-Nhc+oVAHm0uz/PkJAWscwIT4ijTrK5fqNqz9QB1D35SbbuMG1uB6Yr5AJpvPSWg+WOw7nYNswerYh0kOk64gqQ==", "dev": true, "requires": {"natural-compare": "^1.4.0", "semver": "^5.6.0", "vue-eslint-parser": "^7.0.0"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==", "dev": true}}}, "eslint-rule-docs": {"version": "1.1.235", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-rule-docs/-/eslint-rule-docs-1.1.235.tgz", "integrity": "sha512-+TQ+x4JdTnDoFEXXb3fDvfGOwnyNV7duH8fXWTPD1ieaBmB8omj7Gw/pMBBu4uI2uJCCU8APDaQJzWuXnTsH4A==", "dev": true}, "eslint-scope": {"version": "5.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-template-visitor": {"version": "2.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-template-visitor/-/eslint-template-visitor-2.3.2.tgz", "integrity": "sha512-3ydhqFpuV7x1M9EK52BPNj6V0Kwu0KKkcIAfpUhwHbR8ocRln/oUHgfxQupY8O1h4Qv/POHDumb/BwwNfxbtnA==", "dev": true, "requires": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "eslint-visitor-keys": "^2.0.0", "esquery": "^1.3.1", "multimap": "^1.1.0"}, "dependencies": {"eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "dev": true}}}, "eslint-utils": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-utils/-/eslint-utils-2.1.0.tgz", "integrity": "sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==", "dev": true, "requires": {"eslint-visitor-keys": "^1.1.0"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==", "dev": true}, "eslint-webpack-plugin": {"version": "2.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/eslint-webpack-plugin/-/eslint-webpack-plugin-2.6.0.tgz", "integrity": "sha512-V+LPY/T3kur5QO3u+1s34VDTcRxjXWPUGM4hlmTb5DwVD0OQz631yGTxJZf4SpAqAjdbBVe978S8BJeHpAdOhQ==", "dev": true, "requires": {"@types/eslint": "^7.28.2", "arrify": "^2.0.1", "jest-worker": "^27.3.1", "micromatch": "^4.0.4", "normalize-path": "^3.0.0", "schema-utils": "^3.1.1"}}, "espree": {"version": "7.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/espree/-/espree-7.3.1.tgz", "integrity": "sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==", "dev": true, "requires": {"acorn": "^7.4.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^1.3.0"}}, "esprima": {"version": "4.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==", "dev": true}, "esquery": {"version": "1.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/esquery/-/esquery-1.4.2.tgz", "integrity": "sha512-JVSoLdTlTDkmjFmab7H/9SL9qGSyjElT3myyKp7krqjVFQCDLmj1QFaCLRFBszBKI0XVZaiiXvuPIX3ZwHe1Ng==", "dev": true, "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "dev": true}}}, "esrecurse": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "dev": true}}}, "estraverse": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==", "dev": true}, "esutils": {"version": "2.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "dev": true}, "execall": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/execall/-/execall-2.0.0.tgz", "integrity": "sha512-0FU2hZ5Hh6iQnarpRtQurM/aAvp3RIbfvgLHrcqJYzhXyV2KFruhuChf9NC6waAhiUR7FFtlugkI4p7f2Fqlow==", "dev": true, "requires": {"clone-regexp": "^2.1.0"}}, "exit-on-epipe": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz", "integrity": "sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw=="}, "expand-brackets": {"version": "2.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA==", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha512-Rr7<PERSON>jQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}}}, "extend": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "dev": true}, "extend-shallow": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "dev": true, "requires": {"isobject": "^3.0.1"}}}}, "external-editor": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/external-editor/-/external-editor-3.1.0.tgz", "integrity": "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==", "dev": true, "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "extglob": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extglob/-/extglob-2.0.4.tgz", "integrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-1.0.0.tgz", "integrity": "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "fast-glob": {"version": "3.2.12", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fast-glob/-/fast-glob-3.2.12.tgz", "integrity": "sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "dev": true}, "fastest-levenshtein": {"version": "1.0.16", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz", "integrity": "sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==", "dev": true}, "fastq": {"version": "1.15.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fastq/-/fastq-1.15.0.tgz", "integrity": "sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==", "dev": true, "requires": {"reusify": "^1.0.4"}}, "figures": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/figures/-/figures-3.2.0.tgz", "integrity": "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "5.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/file-entry-cache/-/file-entry-cache-5.0.1.tgz", "integrity": "sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==", "dev": true, "requires": {"flat-cache": "^2.0.1"}}, "file-saver": {"version": "2.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/file-saver/-/file-saver-2.0.5.tgz", "integrity": "sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA=="}, "fill-range": {"version": "7.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "flat-cache": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/flat-cache/-/flat-cache-2.0.1.tgz", "integrity": "sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==", "dev": true, "requires": {"flatted": "^2.0.0", "rimraf": "2.6.3", "write": "1.0.3"}}, "flatted": {"version": "2.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/flatted/-/flatted-2.0.2.tgz", "integrity": "sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==", "dev": true}, "follow-redirects": {"version": "1.15.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ=="}, "for-each": {"version": "0.3.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/for-each/-/for-each-0.3.3.tgz", "integrity": "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==", "dev": true, "requires": {"is-callable": "^1.1.3"}}, "for-in": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/for-in/-/for-in-1.0.2.tgz", "integrity": "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==", "dev": true}, "form-data": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/form-data/-/form-data-4.0.2.tgz", "integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "dependencies": {"es-set-tostringtag": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "requires": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}}, "function-bind": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}, "get-intrinsic": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "requires": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}}, "gopd": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="}, "has-symbols": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="}, "has-tostringtag": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "requires": {"has-symbols": "^1.0.3"}}}}, "frac": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/frac/-/frac-1.1.2.tgz", "integrity": "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="}, "fragment-cache": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA==", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "fs-extra": {"version": "8.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fs-extra/-/fs-extra-8.1.0.tgz", "integrity": "sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==", "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true}, "fsevents": {"version": "2.3.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==", "dev": true}, "function.prototype.name": {"version": "1.1.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/function.prototype.name/-/function.prototype.name-1.1.5.tgz", "integrity": "sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.0", "functions-have-names": "^1.2.2"}}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==", "dev": true}, "functions-have-names": {"version": "1.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/functions-have-names/-/functions-have-names-1.2.3.tgz", "integrity": "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true}, "get-caller-file": {"version": "2.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="}, "get-intrinsic": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-intrinsic/-/get-intrinsic-1.2.0.tgz", "integrity": "sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "get-proto": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "requires": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}}, "get-stdin": {"version": "8.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-stdin/-/get-stdin-8.0.0.tgz", "integrity": "sha512-sY22aA6xchAzprjyqmSEQv4UbAAzRN0L2dQB0NlN5acTTK9Don6nhoc3eAbUnpZiCANAMfd/+40kVdKfFygohg==", "dev": true}, "get-symbol-description": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-symbol-description/-/get-symbol-description-1.0.0.tgz", "integrity": "sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}}, "get-value": {"version": "2.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-value/-/get-value-2.0.6.tgz", "integrity": "sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==", "dev": true}, "glob": {"version": "7.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "requires": {"is-glob": "^4.0.1"}}, "glob-to-regexp": {"version": "0.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz", "integrity": "sha512-Iozmtbqv0noj0uDDqoL0zNq0VBEfK2YFoMAZoxJe4cwphvLR+JskfF30QhXHOR4m3KrE6NLRYw+U9MRXvifyig==", "dev": true}, "global-modules": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/global-modules/-/global-modules-2.0.0.tgz", "integrity": "sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A==", "dev": true, "requires": {"global-prefix": "^3.0.0"}}, "global-prefix": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/global-prefix/-/global-prefix-3.0.0.tgz", "integrity": "sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==", "dev": true, "requires": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "dependencies": {"which": {"version": "1.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "globals": {"version": "11.12.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true}, "globalthis": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globalthis/-/globalthis-1.0.3.tgz", "integrity": "sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==", "dev": true, "requires": {"define-properties": "^1.1.3"}}, "globby": {"version": "11.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globby/-/globby-11.1.0.tgz", "integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==", "dev": true, "requires": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "dependencies": {"ignore": {"version": "5.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-5.2.4.tgz", "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "dev": true}}}, "globjoin": {"version": "0.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globjoin/-/globjoin-0.1.4.tgz", "integrity": "sha512-xYfnw62CKG8nLkZBfWbhWwDw02CHty86jfPcc2cr3ZfeuK9ysoVPPEUxf21bAD/rWAgk52SuBrLJlefNy8mvFg==", "dev": true}, "gm-crypto": {"version": "0.1.12", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/gm-crypto/-/gm-crypto-0.1.12.tgz", "integrity": "sha512-ercd9ionBqxR+/FCXICr0eo+jzC8BvSK0j9L7/eB0uwbyjgeMPTdBNrcQTqIuRXOtOAKSGsTNvtLYFnIxNEoFg==", "requires": {"buffer": "^5.7.0", "jsbn": "^1.1.0", "to-arraybuffer": "^1.0.1"}}, "gonzales-pe": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/gonzales-pe/-/gonzales-pe-4.3.0.tgz", "integrity": "sha512-otgSPpUmdWJ43VXyiNgEYE4luzHCL2pz4wQ0OnDluC6Eg4Ko3Vexy/SrSynglw/eR+OhkzmqFCZa/OFa/RgAOQ==", "dev": true, "requires": {"minimist": "^1.2.5"}}, "good-listener": {"version": "1.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/good-listener/-/good-listener-1.2.2.tgz", "integrity": "sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw==", "requires": {"delegate": "^3.1.2"}}, "gopd": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/gopd/-/gopd-1.0.1.tgz", "integrity": "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==", "dev": true, "requires": {"get-intrinsic": "^1.1.3"}}, "graceful-fs": {"version": "4.2.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/graceful-fs/-/graceful-fs-4.2.10.tgz", "integrity": "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA=="}, "h_ui": {"version": "1.60.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/h_ui/-/1.60.0.tgz", "integrity": "sha1-mNLLgUhSRgb4ICPiP+xT+uhqM40=", "requires": {"@babel/runtime-corejs2": "^7.12.1", "async-validator": "^1.6.7", "bignumber.js": "9.0.0", "deepmerge": "^1.4.3", "element-resize-detector": "^1.1.15", "encoding": "0.1.13", "iconv-lite": "0.4.24", "js-calendar": "^1.2.3", "nanoid": "^4.0.2", "popper.js": "^1.16.1", "xlsx": "0.15.5"}, "dependencies": {"nanoid": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/nanoid/-/nanoid-4.0.2.tgz", "integrity": "sha512-7ZtY5KTCNheRGfEFxnedV5zFiORN1+Y1N6zvPTnHQd8ENUvfaDBeuJDZb2bN/oXwXxu3qkTXDzy57W5vAmDTBw=="}}}, "hammerjs": {"version": "2.0.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hammerjs/-/hammerjs-2.0.8.tgz", "integrity": "sha512-tSQXBXS/MWQOn/RKckawJ61vvsDpCom87JgxiYdGwHdOa0ht0vzUWDlfioofFCRU0L+6NGDt6XzbgoJvZkMeRQ=="}, "hard-rejection": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hard-rejection/-/hard-rejection-2.1.0.tgz", "integrity": "sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==", "dev": true}, "has": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-bigints": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-bigints/-/has-bigints-1.0.2.tgz", "integrity": "sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="}, "has-property-descriptors": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz", "integrity": "sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==", "dev": true, "requires": {"get-intrinsic": "^1.1.1"}}, "has-proto": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-proto/-/has-proto-1.0.1.tgz", "integrity": "sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==", "dev": true}, "has-symbols": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "dev": true}, "has-tostringtag": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "integrity": "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "has-value": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-value/-/has-value-1.0.0.tgz", "integrity": "sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw==", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-values/-/has-values-1.0.0.tgz", "integrity": "sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ==", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "is-number": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-number/-/is-number-3.0.0.tgz", "integrity": "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "kind-of": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hasown": {"version": "2.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "requires": {"function-bind": "^1.1.2"}, "dependencies": {"function-bind": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}}}, "hosted-git-info": {"version": "2.8.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "integrity": "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==", "dev": true}, "html-tags": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/html-tags/-/html-tags-2.0.0.tgz", "integrity": "sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==", "dev": true}, "htmlparser2": {"version": "3.10.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/htmlparser2/-/htmlparser2-3.10.1.tgz", "integrity": "sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ==", "dev": true, "requires": {"domelementtype": "^1.3.1", "domhandler": "^2.3.0", "domutils": "^1.5.1", "entities": "^1.1.1", "inherits": "^2.0.1", "readable-stream": "^3.1.1"}}, "hui-core": {"version": "1.47.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hui-core/-/hui-core-1.47.1.tgz", "integrity": "sha512-PJDtTT4cHW3JCQRNscG4n5NLGIONtTze0gnNKIkm7OHx+3THeANCZx0VS1XSkpmsKbaOG/ugNEju0bgjhlxzXA==", "requires": {"hui-logger": "1.0.1", "hui-sdk": "1.47.1", "vue-router": "3.5.0"}}, "hui-logger": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hui-logger/-/hui-logger-1.0.1.tgz", "integrity": "sha512-wpNUNK6wIMJoxk+jKyNogxiXzLLgzbcmOcKHUO1NluI/uFXUzoso3ri5EmqpWBbzzY9f8O5AbDFFLWBmiZEszw=="}, "hui-plugin-jsx": {"version": "1.3.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hui-plugin-jsx/-/hui-plugin-jsx-1.3.3.tgz", "integrity": "sha512-vGFq0X03WwGZKXX8GAS00OoJoCx2FIo9/1iH8wEBfuuS5RI3vK4wRgb7xn+Cd3HdfBTjDE3mlmUcptYCSfAWgg==", "dev": true, "requires": {"@vue/babel-helper-vue-jsx-merge-props": "1.0.0", "@vue/babel-preset-jsx": "1.2.4", "webpack-merge": "4.2.2"}}, "hui-plugin-lint": {"version": "1.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hui-plugin-lint/-/hui-plugin-lint-1.5.1.tgz", "integrity": "sha512-fqKeR318QWykIuLWSpeaFjxDK0PcKIXSgq2DUg/dZcAP58H/VsVpydFLyN4zNKrW1Vq+fsTJYc3xgAkdeF0JNw==", "dev": true, "requires": {"@winner-fed/eslint-config-win": "^1.2.0", "@winner-fed/stylelint-config-win": "^0.1.6", "babel-eslint": "10.1.0", "chalk": "^4.1.0", "eslint": "7.2.0", "eslint-plugin-vue": "6.2.2", "eslint-webpack-plugin": "2.6.0", "fs-extra": "^8.1.0", "ora": "^4.0.4", "pluralize": "^8.0.0", "stylelint": "13.7.0", "table": "^6.7.1", "vue-eslint-parser": "7.1.0"}}, "hui-plugin-micro-app": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hui-plugin-micro-app/-/hui-plugin-micro-app-2.0.0.tgz", "integrity": "sha512-wUQnwGJNcaX0NQeAv4I2pMAaQ+5TaOR5BYljkPbK4ZicfnIy0aiKtijwGLVsczLL8vNGoQc0eKhXKKXMc9nsuQ==", "requires": {"address": "1.1.2", "cheerio": "1.0.0-rc.12", "chokidar": "^3.5.1", "fs-extra": "^8.1.0", "webpack-sources": "1.4.3"}}, "hui-sdk": {"version": "1.47.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/hui-sdk/-/hui-sdk-1.47.1.tgz", "integrity": "sha512-9iP5NqhBZONR9MuQ4vX8WY92dGAiij287dfysKn7PBXt3M9w7l/4kAQtj336M3FzX28YR21WAyJ9CtoK/KLyhw==", "requires": {"axios": "1.8.2", "base64-js": "1.5.1", "hui-logger": "1.0.1"}, "dependencies": {"axios": {"version": "1.8.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/axios/-/axios-1.8.2.tgz", "integrity": "sha512-ls4GYBm5aig9vWx8AWDSGLpnpDQRtWAfrjU+EuytuODrFBkqesN2RkOQCBzrA1RQNHw1SmRMSDDDSwzNAYQ6Rg==", "requires": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="}, "ignore": {"version": "4.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-4.0.6.tgz", "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==", "dev": true}, "immediate": {"version": "3.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/immediate/-/immediate-3.0.6.tgz", "integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="}, "import-fresh": {"version": "3.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "import-lazy": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/import-lazy/-/import-lazy-4.0.0.tgz", "integrity": "sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw==", "dev": true}, "import-modules": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/import-modules/-/import-modules-2.1.0.tgz", "integrity": "sha512-8H<PERSON>WcnkbGpovH9yInoisxaSoIg9Brbul+Ju3Kqe2UsYDUBJD/iQjSgEj0zPcTDPKfPp2fs5xlv1i+JSye/m1/A==", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "dev": true}, "indent-string": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==", "dev": true}, "indexes-of": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/indexes-of/-/indexes-of-1.0.1.tgz", "integrity": "sha512-bup+4tap3Hympa+JBJUG7XuOsdNQ6fxt0MHyXMKuLBKn0OqsTfvUxkUrroEX1+B2VsSHvCjiIcZVxRtYa4nllA==", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "ini": {"version": "1.3.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "dev": true}, "inquirer": {"version": "7.3.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/inquirer/-/inquirer-7.3.3.tgz", "integrity": "sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==", "dev": true, "requires": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.19", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.6.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}}, "internal-slot": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/internal-slot/-/internal-slot-1.0.5.tgz", "integrity": "sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==", "dev": true, "requires": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}}, "irregular-plurals": {"version": "3.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/irregular-plurals/-/irregular-plurals-3.4.0.tgz", "integrity": "sha512-YXxECO/W6N9aMBVKMKKZ8TXESgq7EFrp3emCGGUcrYY1cgJIeZjoB75MTu8qi+NAKntS9NwPU8VdcQ3r6E6aWQ==", "dev": true}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha512-e1BM1qnDbMRG3ll2U9dSK0UMHuWOs3pY3AtcFsmvwPtKL3MML/Q86i+GilLfvqEs4GW+ExB91tQ3Ig9noDIZ+A==", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-alphabetical": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-alphabetical/-/is-alphabetical-1.0.4.tgz", "integrity": "sha512-DwzsA04LQ10FHTZuL0/grVDk4rFoVH1pjAToYwBrHSxcrBIGQuXrQMtD5U1b0U2XVgKZCTLLP8u2Qxqhy3l2Vg==", "dev": true}, "is-alphanumeric": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-alphanumeric/-/is-alphanumeric-1.0.0.tgz", "integrity": "sha512-ZmRL7++ZkcMOfDuWZuMJyIVLr2keE1o/DeNWh1EmgqGhUcV+9BIVsx0BcSBOHTZqzjs4+dISzr2KAeBEWGgXeA==", "dev": true}, "is-alphanumerical": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-alphanumerical/-/is-alphanumerical-1.0.4.tgz", "integrity": "sha512-UzoZUr+XfVz3t3v4KyGEniVL9BDRoQtY7tOyrRybkVNjDFWyo1yhXNGrrBTQxp3ib9BLAWs7k2YKBQsFRkZG9A==", "dev": true, "requires": {"is-alphabetical": "^1.0.0", "is-decimal": "^1.0.0"}}, "is-array-buffer": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-array-buffer/-/is-array-buffer-3.0.1.tgz", "integrity": "sha512-ASfLknmY8Xa2XtB4wmbz13Wu202baeA18cJBCeCy0wXUHZF0IPyVEXqKEcd+t2fNSLLL1vC6k7lxZEojNbISXQ==", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-typed-array": "^1.1.10"}}, "is-arrayish": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="}, "is-bigint": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-bigint/-/is-bigint-1.0.4.tgz", "integrity": "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==", "dev": true, "requires": {"has-bigints": "^1.0.1"}}, "is-binary-path": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "requires": {"binary-extensions": "^2.0.0"}}, "is-boolean-object": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "integrity": "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-buffer": {"version": "2.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-2.0.5.tgz", "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==", "dev": true}, "is-builtin-module": {"version": "3.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-builtin-module/-/is-builtin-module-3.2.1.tgz", "integrity": "sha512-BSLE3HnV2syZ0FK0iMA/yUGplUeMmNz4AW5fnTunbCIqZi4vG3WjJT9FHMy5D69xmAYBHXQhJdALdpwVxV501A==", "dev": true, "requires": {"builtin-modules": "^3.3.0"}}, "is-callable": {"version": "1.2.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==", "dev": true}, "is-core-module": {"version": "2.11.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-core-module/-/is-core-module-2.11.0.tgz", "integrity": "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==", "dev": true, "requires": {"has": "^1.0.3"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha512-+w9D5ulSoBNlmw9OHn3U2v51SyoCd0he+bB3xMl62oijhrspxowjU+AIcDY0N3iEJbUEkB15IlMASQsxYigvXg==", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-date-object/-/is-date-object-1.0.5.tgz", "integrity": "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-decimal": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-decimal/-/is-decimal-1.0.4.tgz", "integrity": "sha512-RGdriMmQQvZ2aqaQq3awNA6dCGtKpiDFcOzrTWrDAT2MiWrKQVPmxLGHl7Y2nNu6led0kEyoX0enY0qXYsv9zw==", "dev": true}, "is-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==", "dev": true}}}, "is-directory": {"version": "0.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-directory/-/is-directory-0.3.1.tgz", "integrity": "sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw==", "dev": true}, "is-extendable": {"version": "0.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "is-glob": {"version": "4.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "requires": {"is-extglob": "^2.1.1"}}, "is-hexadecimal": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-hexadecimal/-/is-hexadecimal-1.0.4.tgz", "integrity": "sha512-gyPJuv83bHMpocVYoqof5VDiZveEoGoFL8m3BXNb2VW8Xs+rz9kqO8LOQ5DH6EsuvilT1ApazU0pyl+ytbPtlw==", "dev": true}, "is-interactive": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-interactive/-/is-interactive-1.0.0.tgz", "integrity": "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==", "dev": true}, "is-negative-zero": {"version": "2.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha512-d<PERSON><PERSON><PERSON><PERSON>awXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==", "dev": true}, "is-number": {"version": "7.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="}, "is-number-object": {"version": "1.0.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-number-object/-/is-number-object-1.0.7.tgz", "integrity": "sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-obj": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-obj/-/is-obj-2.0.0.tgz", "integrity": "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==", "dev": true}, "is-plain-obj": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "integrity": "sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==", "dev": true}, "is-plain-object": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-object/-/is-plain-object-5.0.0.tgz", "integrity": "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==", "dev": true}, "is-regex": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-regexp": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-regexp/-/is-regexp-2.1.0.tgz", "integrity": "sha512-OZ4IlER3zmRIoB9AqNhEggVxqIH4ofDns5nRrPS6yQxXE1TPCUpFznBfRQmQa8uC+pXqjMnukiJBxCisIxiLGA==", "dev": true}, "is-shared-array-buffer": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz", "integrity": "sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==", "dev": true, "requires": {"call-bind": "^1.0.2"}}, "is-string": {"version": "1.0.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-string/-/is-string-1.0.7.tgz", "integrity": "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-supported-regexp-flag": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-supported-regexp-flag/-/is-supported-regexp-flag-1.0.1.tgz", "integrity": "sha512-3vcJecUUrpgCqc/ca0aWeNu64UGgxcvO60K/Fkr1N6RSvfGCTU60UKN68JDmKokgba0rFFJs12EnzOQa14ubKQ==", "dev": true}, "is-symbol": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-symbol/-/is-symbol-1.0.4.tgz", "integrity": "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "is-typed-array": {"version": "1.1.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-typed-array/-/is-typed-array-1.1.10.tgz", "integrity": "sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==", "dev": true}, "is-unicode-supported": {"version": "0.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "dev": true}, "is-weakref": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-weakref/-/is-weakref-1.0.2.tgz", "integrity": "sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==", "dev": true, "requires": {"call-bind": "^1.0.2"}}, "is-whitespace-character": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-whitespace-character/-/is-whitespace-character-1.0.4.tgz", "integrity": "sha512-SDweEzfIZM0SJV0EUga669UTKlmL0Pq8Lno0QDQsPnvECB3IM2aP0gdx5TrU0A01MAPfViaZiI2V1QMZLaKK5w==", "dev": true}, "is-windows": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==", "dev": true}, "is-word-character": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-word-character/-/is-word-character-1.0.4.tgz", "integrity": "sha512-5SMO8RVennx3nZrqtKwCGyyetPE9VDba5ugvKLaD4KopPG5kR4mQ7tNt/r7feL5yt5h3lpuBbIUmCOG2eSzXHA==", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="}, "isexe": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="}, "isobject": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "dev": true}, "jest-worker": {"version": "27.5.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jest-worker/-/jest-worker-27.5.1.tgz", "integrity": "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==", "dev": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true}, "supports-color": {"version": "8.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "js-calendar": {"version": "1.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/js-calendar/-/js-calendar-1.2.3.tgz", "integrity": "sha512-dAA1/Zbp4+c5E+ARCVTIuKepXsNLzSYfzvOimiYD4S5eeP9QuplSHLcdhfqFSwyM1o1u6ku6RRRCyaZ0YAjiBw=="}, "js-tokens": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "js-yaml": {"version": "3.14.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsbn/-/jsbn-1.1.0.tgz", "integrity": "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A=="}, "jsep": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsep/-/jsep-1.4.0.tgz", "integrity": "sha512-B7qPcEVE3NVkmSJbaYxvv4cHkVW7DQsZz13pUMrfS8z8Q/BuShN+gcTXrUlPiGqM2/t/EEaI030bpxMqY8gMlw=="}, "jsesc": {"version": "2.5.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==", "dev": true}, "json5": {"version": "2.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="}, "json5-to-table": {"version": "0.1.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json5-to-table/-/json5-to-table-0.1.8.tgz", "integrity": "sha512-TVp3iBrjnPRUNYII+BiZCpfAi/tMD+pa41IccOSEVLsn5L+vpjEJ2CzNXHcScFknBj3svxicqzAeRQ1NsAy2SA==", "requires": {"json5": "^2.2.0", "lodash": "^4.17.21", "yargs": "^17.1.1"}, "dependencies": {"cliui": {"version": "8.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "yargs": {"version": "17.7.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="}}}, "jsonfile": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==", "requires": {"graceful-fs": "^4.1.6"}}, "jsonlint-mod": {"version": "1.7.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsonlint-mod/-/jsonlint-mod-1.7.6.tgz", "integrity": "sha512-oGuk6E1ehmIpw0w9ttgb2KsDQQgGXBzZczREW8OfxEm9eCQYL9/LCexSnh++0z3AiYGcXpBgqDSx9AAgzl/Bvg==", "requires": {"JSV": "^4.0.2", "chalk": "^2.4.2", "underscore": "^1.9.1"}, "dependencies": {"chalk": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}}}, "jsonpath-plus": {"version": "10.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsonpath-plus/-/jsonpath-plus-10.3.0.tgz", "integrity": "sha512-8TNmfeTCk2Le33A3vRRwtuworG/L5RrgMvdjhKZxvyShO+mBu2fP50OWUjRLNtvw344DdDarFh9buFAZs5ujeA==", "requires": {"@jsep-plugin/assignment": "^1.3.0", "@jsep-plugin/regex": "^1.0.4", "jsep": "^1.4.0"}}, "jszip": {"version": "3.10.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jszip/-/jszip-3.10.1.tgz", "integrity": "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==", "requires": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}, "dependencies": {"readable-stream": {"version": "2.3.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "string_decoder": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}}}, "keycharm": {"version": "0.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/keycharm/-/keycharm-0.2.0.tgz", "integrity": "sha512-i/XBRTiLqRConPKioy2oq45vbv04e8x59b0mnsIRQM+7Ec/8BC7UcL5pnC4FMeGb8KwG7q4wOMw7CtNZf5tiIg=="}, "kind-of": {"version": "6.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "dev": true}, "known-css-properties": {"version": "0.19.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/known-css-properties/-/known-css-properties-0.19.0.tgz", "integrity": "sha512-eYboRV94Vco725nKMlpkn3nV2+96p9c3gKXRsYqAJSswSENvBhN7n5L+uDhY58xQa0UukWsDMTGELzmD8Q+wTA==", "dev": true}, "ldjson-stream": {"version": "1.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ldjson-stream/-/ldjson-stream-1.2.1.tgz", "integrity": "sha512-xw/nNEXafuPSLu8NjjG3+atVVw+8U1APZAQylmwQn19Hgw6rC7QjHvP6MupnHWCrzSm9m0xs5QWkCLuRvBPjgQ==", "dev": true, "requires": {"split2": "^0.2.1", "through2": "^0.6.1"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "dev": true}, "through2": {"version": "0.6.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/through2/-/through2-0.6.5.tgz", "integrity": "sha512-RkK/CCESdTKQZHdmKICijdKKsCRVHs5KsLZ6pACAmF/1GPUQhonHSXWNERctxEp7RmvjdNbZTL5z9V7nSCXKcg==", "dev": true, "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "leven": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/leven/-/leven-2.1.0.tgz", "integrity": "sha512-nvVPLpIHUxCUoRLrFqTgSxXJ614d8AgQoWl7zPe/2VadE8+1dpU3LBhowRuBAcuwruWtOdD8oYC9jDNJjXDPyA==", "dev": true}, "levn": {"version": "0.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/levn/-/levn-0.4.1.tgz", "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "dev": true, "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "lie": {"version": "3.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lie/-/lie-3.3.0.tgz", "integrity": "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==", "requires": {"immediate": "~3.0.5"}}, "lines-and-columns": {"version": "1.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "dev": true}, "load-json-file": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/load-json-file/-/load-json-file-4.0.0.tgz", "integrity": "sha512-Kx8hMakjX03tiGTLAIdJ+lL0htKnXjEZN6hk/tozf/WOuYGdZBJrZ+rCJRbVCugsjB3jMLn9746NsQIf5VjBMw==", "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}, "dependencies": {"parse-json": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==", "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "pify": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pify/-/pify-3.0.0.tgz", "integrity": "sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg=="}}}, "loader-utils": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/loader-utils/-/loader-utils-2.0.4.tgz", "integrity": "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==", "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.21", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "lodash.debounce": {"version": "4.0.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==", "dev": true}, "lodash.kebabcase": {"version": "4.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz", "integrity": "sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==", "dev": true}, "lodash.merge": {"version": "4.6.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "dev": true}, "lodash.truncate": {"version": "4.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lodash.truncate/-/lodash.truncate-4.4.2.tgz", "integrity": "sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==", "dev": true}, "log-symbols": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/log-symbols/-/log-symbols-3.0.0.tgz", "integrity": "sha512-dSkNGuI7iG3mfvDzUuYZyvk5dD9ocYCYzNU6CYDE6+Xqd+gwme6Z00NS3dUh8mq/73HaEtT7m6W+yUPtU6BZnQ==", "dev": true, "requires": {"chalk": "^2.4.2"}, "dependencies": {"chalk": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}}}, "longest-streak": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/longest-streak/-/longest-streak-2.0.4.tgz", "integrity": "sha512-vM6rUVCVUJJt33bnmHiZEvr7wPT78ztX7rojL+LW51bHtLh6HTjx84LA5W4+oa6aKEJA7jJu5LR6vQRBpA5DVg==", "dev": true}, "loud-rejection": {"version": "1.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/loud-rejection/-/loud-rejection-1.6.0.tgz", "integrity": "sha512-RPNliZOFkqFumDhvYqOaNY4Uz9oJM2K9tC6JWsJJsNdhuONW4LQHRBpb0qf4pJApVffI5N39SwzWZJuEhfd7eQ==", "dev": true, "requires": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}}, "lru-cache": {"version": "6.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dev": true, "requires": {"yallist": "^4.0.0"}}, "map-cache": {"version": "0.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==", "dev": true}, "map-obj": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/map-obj/-/map-obj-4.3.0.tgz", "integrity": "sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w==", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "markdown-escapes": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/markdown-escapes/-/markdown-escapes-1.0.4.tgz", "integrity": "sha512-8z4efJYk43E0upd0NbVXwgSTQs6cT3T06etieCMEg7dRbzCbxUCK/GHlX8mhHRDcp+OLlHkPKsvqQTCvsRl2cg==", "dev": true}, "markdown-table": {"version": "1.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/markdown-table/-/markdown-table-1.1.3.tgz", "integrity": "sha512-1RUZVgQlpJSPWYbFSpmudq5nHY1doEIv89gBtF0s4gW1GF2XorxcA/70M5vq7rLv0a6mhOUccRsqkwhwLCIQ2Q==", "dev": true}, "math-intrinsics": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="}, "mathml-tag-names": {"version": "2.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz", "integrity": "sha512-APMBEanjybaPzUrfqU0IMU5I0AswKMH7k8OTLs0vvV4KZpExkTkY87nR/zpbuTPj+gARop7aGUbl11pnDfW6xg==", "dev": true}, "mdast-util-compact": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mdast-util-compact/-/mdast-util-compact-1.0.4.tgz", "integrity": "sha512-3YDMQHI5vRiS2uygEFYaqckibpJtKq5Sj2c8JioeOQBU6INpKbdWzfyLqFFnDwEcEnRFIdMsguzs5pC1Jp4Isg==", "dev": true, "requires": {"unist-util-visit": "^1.1.0"}}, "mdast-util-from-markdown": {"version": "0.8.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mdast-util-from-markdown/-/mdast-util-from-markdown-0.8.5.tgz", "integrity": "sha512-2hkTXtYYnr+NubD/g6KGBS/0mFmBcifAsI0yIWRiRo0PjVs6SSOSOdtzbp6kSGnShDN6G5aWZpKQ2lWRy27mWQ==", "dev": true, "requires": {"@types/mdast": "^3.0.0", "mdast-util-to-string": "^2.0.0", "micromark": "~2.11.0", "parse-entities": "^2.0.0", "unist-util-stringify-position": "^2.0.0"}}, "mdast-util-to-markdown": {"version": "0.6.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mdast-util-to-markdown/-/mdast-util-to-markdown-0.6.5.tgz", "integrity": "sha512-XeV9sDE7ZlOQvs45C9UKMtfTcctcaj/pGwH8YLbMHoMOXNNCn2LsqVQOqrF1+/NU8lKDAqozme9SCXWyo9oAcQ==", "dev": true, "requires": {"@types/unist": "^2.0.0", "longest-streak": "^2.0.0", "mdast-util-to-string": "^2.0.0", "parse-entities": "^2.0.0", "repeat-string": "^1.0.0", "zwitch": "^1.0.0"}}, "mdast-util-to-string": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mdast-util-to-string/-/mdast-util-to-string-2.0.0.tgz", "integrity": "sha512-AW4DRS3QbBayY/jJmD8437V1Gombjf8RSOUCMFBuo5iHi58AGEgVCKQ+ezHkZZDpAQS75hcBMpLqjpJTjtUL7w==", "dev": true}, "meow": {"version": "7.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/meow/-/meow-7.1.1.tgz", "integrity": "sha512-GWHvA5QOcS412WCo8vwKDlTelGLsCGBVevQB5Kva961rmNfun0PCbv5+xta2kUMFJyR8/oWnn7ddeKdosbAPbA==", "dev": true, "requires": {"@types/minimist": "^1.2.0", "camelcase-keys": "^6.2.2", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^2.5.0", "read-pkg-up": "^7.0.1", "redent": "^3.0.0", "trim-newlines": "^3.0.0", "type-fest": "^0.13.1", "yargs-parser": "^18.1.3"}, "dependencies": {"type-fest": {"version": "0.13.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.13.1.tgz", "integrity": "sha512-34R7HTnG0XIJcBSn5XhDd7nNFPRcXYRZrBB2O2jdKqYODldSzBAqzsWoZYYvduky73toYS/ESqxPvkDf/F0XMg==", "dev": true}}}, "merge-stream": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "dev": true}, "merge2": {"version": "1.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true}, "micromark": {"version": "2.11.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/micromark/-/micromark-2.11.4.tgz", "integrity": "sha512-+WoovN/ppKolQOFIAajxi7Lu9kInbPxFuTBVEavFcL8eAfVstoc5MocPmqBeAdBOJV00uaVjegzH4+MA0DN/uA==", "dev": true, "requires": {"debug": "^4.0.0", "parse-entities": "^2.0.0"}}, "micromatch": {"version": "4.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "dev": true, "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mime-db": {"version": "1.52.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types": {"version": "2.1.35", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "requires": {"mime-db": "1.52.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "dev": true}, "min-indent": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/min-indent/-/min-indent-1.0.1.tgz", "integrity": "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==", "dev": true}, "minimatch": {"version": "3.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true}, "minimist-options": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/minimist-options/-/minimist-options-4.1.0.tgz", "integrity": "sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==", "dev": true, "requires": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}, "dependencies": {"arrify": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/arrify/-/arrify-1.0.1.tgz", "integrity": "sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==", "dev": true}, "is-plain-obj": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "integrity": "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==", "dev": true}}}, "mixin-deep": {"version": "1.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mixin-deep/-/mixin-deep-1.3.2.tgz", "integrity": "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "dev": true, "requires": {"isobject": "^3.0.1"}}}}, "mkdirp": {"version": "0.5.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "dev": true, "requires": {"minimist": "^1.2.6"}}, "moment": {"version": "2.29.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w=="}, "monaco-editor": {"version": "0.30.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/monaco-editor/-/monaco-editor-0.30.0.tgz", "integrity": "sha512-/k++/ofRmwnwWTpOWYOMGVcqBrqrlt3MP0Mt/cRTQojW7A9fnekcvPQ2iIFA0YSZdPWPN9yYXrYq0xqiUuxT/A=="}, "monaco-editor-webpack-plugin": {"version": "6.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/monaco-editor-webpack-plugin/-/monaco-editor-webpack-plugin-6.0.0.tgz", "integrity": "sha512-vC886Mzpd2AkSM35XLkfQMjH+Ohz6RISVwhAejDUzZDheJAiz6G34lky1vyO8fZ702v7IrcKmsGwL1rRFnwvUA==", "requires": {"loader-utils": "^2.0.0"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "multimap": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/multimap/-/multimap-1.1.0.tgz", "integrity": "sha512-0ZIR9PasPxGXmRsEF8jsDzndzHDj7tIav+JUmvIFB/WHswliFnquxECT/De7GR4yg99ky/NlRKJT82G1y271bw==", "dev": true}, "multimatch": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/multimatch/-/multimatch-5.0.0.tgz", "integrity": "sha512-ypMKuglUrZUD99Tk2bUQ+xNQj43lPEfAeX2o9cTteAmShXy2VHDJpuwu1o0xqoKCt9jLVAvwyFKdLTPXKAfJyA==", "dev": true, "requires": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}, "dependencies": {"@types/minimatch": {"version": "3.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@types/minimatch/-/minimatch-3.0.5.tgz", "integrity": "sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ==", "dev": true}}}, "mute-stream": {"version": "0.0.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/mute-stream/-/mute-stream-0.0.8.tgz", "integrity": "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==", "dev": true}, "nanoid": {"version": "3.3.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/nanoid/-/nanoid-3.3.4.tgz", "integrity": "sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw=="}, "nanomatch": {"version": "1.2.13", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "natural-compare": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "dev": true}, "node-releases": {"version": "2.0.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/node-releases/-/node-releases-2.0.10.tgz", "integrity": "sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w==", "dev": true}, "node-rsa": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/node-rsa/-/node-rsa-1.1.1.tgz", "integrity": "sha512-Jd4cvbJMryN21r5HgxQOpMEqv+ooke/korixNNK3mGqfGJmy0M77WDDzo/05969+OkMy3XW1UuZsSmW9KQm7Fw==", "requires": {"asn1": "^0.2.4"}}, "node-sql-parser": {"version": "4.11.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/node-sql-parser/-/node-sql-parser-4.11.0.tgz", "integrity": "sha512-ElheoPibjc7IVyRdsORgkzJi0DWm3f0LYSsm/eJIeUt3M/csDLTblLDR4zl5Qi7jmVjJ1KpEkPKSbgVGEzU5Xw==", "requires": {"big-integer": "^1.6.48"}}, "normalize-package-data": {"version": "2.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "integrity": "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==", "dev": true}}}, "normalize-path": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="}, "normalize-range": {"version": "0.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/normalize-range/-/normalize-range-0.1.2.tgz", "integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==", "dev": true}, "normalize-scss": {"version": "7.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/normalize-scss/-/normalize-scss-7.0.1.tgz", "integrity": "sha512-qj16bWnYs+9/ac29IgGjySg4R5qQTp1lXfm7ApFOZNVBYFY8RZ3f8+XQNDDLHeDtI3Ba7Jj4+LuPgz9v/fne2A=="}, "normalize-selector": {"version": "0.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/normalize-selector/-/normalize-selector-0.2.0.tgz", "integrity": "sha512-dxvWdI8gw6eAvk9BlPffgEoGfM7AdijoCwOEJge3e3ulT2XLgmU7KvvxprOaCu05Q1uGRHmOhHe1r6emZoKyFw==", "dev": true}, "nth-check": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/nth-check/-/nth-check-2.1.1.tgz", "integrity": "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==", "requires": {"boolbase": "^1.0.0"}}, "num2fraction": {"version": "1.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/num2fraction/-/num2fraction-1.2.2.tgz", "integrity": "sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg==", "dev": true}, "object-copy": {"version": "0.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ==", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha512-Rr7<PERSON>jQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-inspect": {"version": "1.12.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object-inspect/-/object-inspect-1.12.3.tgz", "integrity": "sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==", "dev": true}, "object-keys": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "dev": true}, "object-visit": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA==", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}}, "object.entries": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object.entries/-/object.entries-1.1.6.tgz", "integrity": "sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "object.pick": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==", "dev": true, "requires": {"isobject": "^3.0.1"}}, "object.values": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/object.values/-/object.values-1.1.6.tgz", "integrity": "sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "once": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/onetime/-/onetime-5.1.2.tgz", "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "optionator": {"version": "0.9.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/optionator/-/optionator-0.9.1.tgz", "integrity": "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==", "dev": true, "requires": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}}, "ora": {"version": "4.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ora/-/ora-4.1.1.tgz", "integrity": "sha512-sjYP8QyVWBpBZWD6Vr1M/KwknSw6kJOz41tvGMlwWeClHBtYKTbHMki1PsLZnxKpXMPbTKv9b3pjQu3REib96A==", "dev": true, "requires": {"chalk": "^3.0.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0", "log-symbols": "^3.0.0", "mute-stream": "0.0.8", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-3.0.0.tgz", "integrity": "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "dev": true}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "dev": true}, "pako": {"version": "1.0.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pako/-/pako-1.0.11.tgz", "integrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="}, "parent-module": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dev": true, "requires": {"callsites": "^3.0.0"}}, "parse-entities": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse-entities/-/parse-entities-2.0.0.tgz", "integrity": "sha512-kkywGpCcRYhqQIchaWqZ875wzpS/bMKhz5HnN3p7wveJTkTtyAB/AlnS0f8DFSqYW1T82t6yEAkEcB+A1I3MbQ==", "dev": true, "requires": {"character-entities": "^1.0.0", "character-entities-legacy": "^1.0.0", "character-reference-invalid": "^1.0.0", "is-alphanumerical": "^1.0.0", "is-decimal": "^1.0.0", "is-hexadecimal": "^1.0.0"}}, "parse-json": {"version": "5.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "parse5": {"version": "7.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse5/-/parse5-7.2.1.tgz", "integrity": "sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==", "requires": {"entities": "^4.5.0"}, "dependencies": {"entities": {"version": "4.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="}}}, "parse5-htmlparser2-tree-adapter": {"version": "7.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-7.1.0.tgz", "integrity": "sha512-ruw5xyKs6lrpo9x9rCZqZZnIUntICjQAd0Wsmp396Ul9lN/h+ifgVV1x1gZHi8euej6wTfpqX8j+BFQxF0NS/g==", "requires": {"domhandler": "^5.0.3", "parse5": "^7.0.0"}, "dependencies": {"domelementtype": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="}, "domhandler": {"version": "5.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/domhandler/-/domhandler-5.0.3.tgz", "integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "requires": {"domelementtype": "^2.3.0"}}}}, "pascalcase": {"version": "0.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw==", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q==", "dev": true}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true}, "path-key": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="}, "path-parse": {"version": "1.0.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true}, "path-type": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="}, "picomatch": {"version": "2.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="}, "pify": {"version": "4.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pify/-/pify-4.0.1.tgz", "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==", "dev": true}, "pkg-conf": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pkg-conf/-/pkg-conf-2.1.0.tgz", "integrity": "sha512-C+VUP+8jis7EsQZIhDYmS5qlNtjv2yP4SNtjXK9AP1ZcTRlnSfuumaTnRfYZnYgUUYVIKqL0fRvmUGDV2fmp6g==", "requires": {"find-up": "^2.0.0", "load-json-file": "^4.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/find-up/-/find-up-2.1.0.tgz", "integrity": "sha512-NWzkk0jSJtTt08+FBFMvXoeZnOJD+jTtsRmBYbAIzJdX6l7dLgR7CTubCM5/eDdPUBvLCeVasP1brfVR/9/EZQ==", "requires": {"locate-path": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha512-NCI2kiDkyR7VeEKm27Kda/iQHyKJe1Bu0FlTbYp3CqJu+9IFe9bLyAjMxf5ZDDbEg+iMPzB5zYyUTSm8wVTKmA==", "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-limit/-/p-limit-1.3.0.tgz", "integrity": "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==", "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-locate/-/p-locate-2.0.0.tgz", "integrity": "sha512-nQja7m7gSKuewoVRen45CtVfODR3crN3goVQ0DDZ9N3yHxgpkuBhZqsaiotSQRrADUrne346peY7kT3TSACykg==", "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-try/-/p-try-1.0.0.tgz", "integrity": "sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww=="}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ=="}}}, "plur": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/plur/-/plur-4.0.0.tgz", "integrity": "sha512-4UGewrYgqDFw9vV6zNV+ADmPAUAfJPKtGvb/VdpQAx25X5f3xXdGdyOEVFwkl8Hl/tl7+xbeHqSEM+D5/TirUg==", "dev": true, "requires": {"irregular-plurals": "^3.2.0"}}, "pluralize": {"version": "8.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pluralize/-/pluralize-8.0.0.tgz", "integrity": "sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==", "dev": true}, "popper.js": {"version": "1.16.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/popper.js/-/popper.js-1.16.1.tgz", "integrity": "sha512-Wb4p1J4zyFTbM+u6WuO4XstYx4Ky9Cewe4DWrel7B0w6VVICvPwdOpotjzcf6eD8TsckVnIMNONQyPIUFOUbCQ=="}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg==", "dev": true}, "postcss": {"version": "8.4.21", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-8.4.21.tgz", "integrity": "sha512-tP7u/Sn/dVxK2NnruI4H9BG+x+Wxz6oeZ1cJ8P6G/PZY0IKk4k/63TDsQf2kQq3+qoJeLm2kIBUNlZe3zgb4Zg==", "requires": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}}, "postcss-html": {"version": "0.36.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-html/-/postcss-html-0.36.0.tgz", "integrity": "sha512-HeiOxGcuwID0AFsNAL0ox3mW6MHH5cstWN1Z3Y+n6H+g12ih7LHdYxWwEA/QmrebctLjo79xz9ouK3MroHwOJw==", "dev": true, "requires": {"htmlparser2": "^3.10.0"}}, "postcss-jsx": {"version": "0.36.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-jsx/-/postcss-jsx-0.36.4.tgz", "integrity": "sha512-jwO/7qWUvYuWYnpOb0+4bIIgJt7003pgU3P6nETBLaOyBXuTD55ho21xnals5nBrlpTIFodyd3/jBi6UO3dHvA==", "dev": true, "requires": {"@babel/core": ">=7.2.2"}}, "postcss-less": {"version": "3.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-less/-/postcss-less-3.1.4.tgz", "integrity": "sha512-7TvleQWNM2QLcHqvudt3VYjULVB49uiW6XzEUFmvwHzvsOEF5MwBrIXZDJQvJNFGjJQTzSzZnDoCJ8h/ljyGXA==", "dev": true, "requires": {"postcss": "^7.0.14"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "postcss-markdown": {"version": "0.36.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-markdown/-/postcss-markdown-0.36.0.tgz", "integrity": "sha512-rl7fs1r/LNSB2bWRhyZ+lM/0bwKv9fhl38/06gF6mKMo/NPnp55+K1dSTosSVjFZc0e1ppBlu+WT91ba0PMBfQ==", "dev": true, "requires": {"remark": "^10.0.1", "unist-util-find-all-after": "^1.0.2"}, "dependencies": {"is-plain-obj": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "integrity": "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==", "dev": true}, "parse-entities": {"version": "1.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse-entities/-/parse-entities-1.2.2.tgz", "integrity": "sha512-NzfpbxW/NPrzZ/yYSoQxyqUZMZXIdCfE0OIN4ESsnptHJECoUk3FZktxNuzQf4tjt5UEopnxpYJbvYuxIFDdsg==", "dev": true, "requires": {"character-entities": "^1.0.0", "character-entities-legacy": "^1.0.0", "character-reference-invalid": "^1.0.0", "is-alphanumerical": "^1.0.0", "is-decimal": "^1.0.0", "is-hexadecimal": "^1.0.0"}}, "remark": {"version": "10.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/remark/-/remark-10.0.1.tgz", "integrity": "sha512-E6lMuoLIy2TyiokHprMjcWNJ5UxfGQjaMSMhV+f4idM625UjjK4j798+gPs5mfjzDE6vL0oFKVeZM6gZVSVrzQ==", "dev": true, "requires": {"remark-parse": "^6.0.0", "remark-stringify": "^6.0.0", "unified": "^7.0.0"}}, "remark-parse": {"version": "6.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/remark-parse/-/remark-parse-6.0.3.tgz", "integrity": "sha512-QbDXWN4HfKTUC0hHa4teU463KclLAnwpn/FBn87j9cKYJWWawbiLgMfP2Q4XwhxxuuuOxHlw+pSN0OKuJwyVvg==", "dev": true, "requires": {"collapse-white-space": "^1.0.2", "is-alphabetical": "^1.0.0", "is-decimal": "^1.0.0", "is-whitespace-character": "^1.0.0", "is-word-character": "^1.0.0", "markdown-escapes": "^1.0.0", "parse-entities": "^1.1.0", "repeat-string": "^1.5.4", "state-toggle": "^1.0.0", "trim": "0.0.1", "trim-trailing-lines": "^1.0.0", "unherit": "^1.0.4", "unist-util-remove-position": "^1.0.0", "vfile-location": "^2.0.0", "xtend": "^4.0.1"}}, "remark-stringify": {"version": "6.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/remark-stringify/-/remark-stringify-6.0.4.tgz", "integrity": "sha512-eRWGdEPMVudijE/psbIDNcnJLRVx3xhfuEsTDGgH4GsFF91dVhw5nhmnBppafJ7+NWINW6C7ZwWbi30ImJzqWg==", "dev": true, "requires": {"ccount": "^1.0.0", "is-alphanumeric": "^1.0.0", "is-decimal": "^1.0.0", "is-whitespace-character": "^1.0.0", "longest-streak": "^2.0.1", "markdown-escapes": "^1.0.0", "markdown-table": "^1.1.0", "mdast-util-compact": "^1.0.0", "parse-entities": "^1.0.2", "repeat-string": "^1.5.4", "state-toggle": "^1.0.0", "stringify-entities": "^1.0.1", "unherit": "^1.0.4", "xtend": "^4.0.1"}}, "unified": {"version": "7.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unified/-/unified-7.1.0.tgz", "integrity": "sha512-lbk82UOIGuCEsZhPj8rNAkXSDXd6p0QLzIuSsCdxrqnqU56St4eyOB+AlXsVgVeRmetPTYydIuvFfpDIed8mqw==", "dev": true, "requires": {"@types/unist": "^2.0.0", "@types/vfile": "^3.0.0", "bail": "^1.0.0", "extend": "^3.0.0", "is-plain-obj": "^1.1.0", "trough": "^1.0.0", "vfile": "^3.0.0", "x-is-string": "^0.1.0"}}, "unist-util-find-all-after": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-find-all-after/-/unist-util-find-all-after-1.0.5.tgz", "integrity": "sha512-lWgIc3rrTMTlK1Y0hEuL+k+ApzFk78h+lsaa2gHf63Gp5Ww+mt11huDniuaoq1H+XMK2lIIjjPkncxXcDp3QDw==", "dev": true, "requires": {"unist-util-is": "^3.0.0"}}, "unist-util-is": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-is/-/unist-util-is-3.0.0.tgz", "integrity": "sha512-sVZZX3+kspVNmLWBPAB6r+7D9ZgAFPNWm66f7YNb420RlQSbn+n8rG8dGZSkrER7ZIXGQYNm5pqC3v3HopH24A==", "dev": true}, "unist-util-stringify-position": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-stringify-position/-/unist-util-stringify-position-1.1.2.tgz", "integrity": "sha512-pNCVrk64LZv1kElr0N1wPiHEUoXNVFERp+mlTg/s9R5Lwg87f9bM/3sQB99w+N9D/qnM9ar3+AKDBwo/gm/iQQ==", "dev": true}, "vfile": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vfile/-/vfile-3.0.1.tgz", "integrity": "sha512-y7Y3gH9BsUSdD4KzHsuMaCzRjglXN0W2EcMf0gpvu6+SbsGhMje7xDc8AEoeXy6mIwCKMI6BkjMsRjzQbhMEjQ==", "dev": true, "requires": {"is-buffer": "^2.0.0", "replace-ext": "1.0.0", "unist-util-stringify-position": "^1.0.0", "vfile-message": "^1.0.0"}}, "vfile-message": {"version": "1.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vfile-message/-/vfile-message-1.1.1.tgz", "integrity": "sha512-1WmsopSGhWt5laNir+633LszXvZ+Z/lxveBf6yhGsqnQIhlhzooZae7zV6YVM1Sdkw68dtAW3ow0pOdPANugvA==", "dev": true, "requires": {"unist-util-stringify-position": "^1.1.1"}}}}, "postcss-media-query-parser": {"version": "0.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz", "integrity": "sha512-3sOlxmbKcSHMjlUXQZKQ06jOswE7oVkXPxmZdoB1r5l0q6gTFTQSHxNxOrCccElbW7dxNytifNEo8qidX2Vsig==", "dev": true}, "postcss-reporter": {"version": "6.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-reporter/-/postcss-reporter-6.0.1.tgz", "integrity": "sha512-LpmQjfRWyabc+fRygxZjpRxfhRf9u/fdlKf4VHG4TSPbV2XNsuISzYW1KL+1aQzx53CAppa1bKG4APIB/DOXXw==", "dev": true, "requires": {"chalk": "^2.4.1", "lodash": "^4.17.11", "log-symbols": "^2.2.0", "postcss": "^7.0.7"}, "dependencies": {"chalk": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "log-symbols": {"version": "2.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/log-symbols/-/log-symbols-2.2.0.tgz", "integrity": "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==", "dev": true, "requires": {"chalk": "^2.0.1"}}, "picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "postcss-resolve-nested-selector": {"version": "0.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.1.tgz", "integrity": "sha512-HvExULSwLqHLgUy1rl3ANIqCsvMS0WHss2UOsXhXnQaZ9VCc2oBvIpXrl00IUFT5ZDITME0o6oiXeiHr2SAIfw==", "dev": true}, "postcss-safe-parser": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-safe-parser/-/postcss-safe-parser-4.0.2.tgz", "integrity": "sha512-Uw6ekxSWNLCPesSv/cmqf2bY/77z11O7jZGPax3ycZMFU/oi2DMH9i89AdHc1tRwFg/arFoEwX0IS3LCUxJh1g==", "dev": true, "requires": {"postcss": "^7.0.26"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "postcss-sass": {"version": "0.4.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-sass/-/postcss-sass-0.4.4.tgz", "integrity": "sha512-BYxnVYx4mQooOhr+zer0qWbSPYnarAy8ZT7hAQtbxtgVf8gy+LSLT/hHGe35h14/pZDTw1DsxdbrwxBN++H+fg==", "dev": true, "requires": {"gonzales-pe": "^4.3.0", "postcss": "^7.0.21"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "postcss-scss": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-scss/-/postcss-scss-2.1.1.tgz", "integrity": "sha512-jQmGnj0hSGLd9RscFw9LyuSVAa5Bl1/KBPqG1NQw9w8ND55nY4ZEsdlVuYJvLPpV+y0nwTV5v/4rHPzZRihQbA==", "dev": true, "requires": {"postcss": "^7.0.6"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "postcss-selector-parser": {"version": "6.0.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-selector-parser/-/postcss-selector-parser-6.0.11.tgz", "integrity": "sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g==", "dev": true, "requires": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}}, "postcss-sorting": {"version": "5.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-sorting/-/postcss-sorting-5.0.1.tgz", "integrity": "sha512-Y9fUFkIhfrm6i0Ta3n+89j56EFqaNRdUKqXyRp6kvTcSXnmgEjaVowCXH+JBe9+YKWqd4nc28r2sgwnzJalccA==", "dev": true, "requires": {"lodash": "^4.17.14", "postcss": "^7.0.17"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "postcss-syntax": {"version": "0.36.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-syntax/-/postcss-syntax-0.36.2.tgz", "integrity": "sha512-nBRg/i7E3SOHWxF3PpF5WnJM/jQ1YpY9000OaVXlAQj6Zp/kIqJxEDWIZ67tAd7NLuk7zqN4yqe9nc0oNAOs1w==", "dev": true}, "postcss-value-parser": {"version": "4.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "dev": true}, "prelude-ls": {"version": "1.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==", "dev": true}, "prettier-plugin-style-order": {"version": "0.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/prettier-plugin-style-order/-/prettier-plugin-style-order-0.2.2.tgz", "integrity": "sha512-4rASdHONhHLNX0arKqEvjGOEAbkSZE3+GQPSaS8mf3en/spjeN0le5fV1yKltWwQeM4UPn7CkKzeD97a4Az/6w==", "dev": true, "requires": {"postcss-sorting": "5.0.1", "resolve-cwd": "^3.0.0"}}, "printj": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/printj/-/printj-1.1.2.tgz", "integrity": "sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ=="}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "progress": {"version": "2.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "dev": true}, "propagating-hammerjs": {"version": "1.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/propagating-hammerjs/-/propagating-hammerjs-1.5.0.tgz", "integrity": "sha512-3PUXWmomwutoZfydC+lJwK1bKCh6sK6jZGB31RUX6+4EXzsbkDZrK4/sVR7gBrvJaEIwpTVyxQUAd29FKkmVdw==", "requires": {"hammerjs": "^2.0.8"}}, "proxy-from-env": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "punycode": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/punycode/-/punycode-2.3.0.tgz", "integrity": "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA=="}, "queue-microtask": {"version": "1.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true}, "quick-lru": {"version": "4.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/quick-lru/-/quick-lru-4.0.1.tgz", "integrity": "sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==", "dev": true}, "raw-loader": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/raw-loader/-/raw-loader-4.0.2.tgz", "integrity": "sha512-ZnScIV3ag9A4wPX/ZayxL/jZH+euYb6FcUinPcgiQW0+UBtEv0O6Q3lGd3cqJ+GHH+rksEv3Pj99oxJ3u3VIKA==", "requires": {"loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}}, "read-pkg": {"version": "5.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/read-pkg/-/read-pkg-5.2.0.tgz", "integrity": "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==", "dev": true, "requires": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "dependencies": {"type-fest": {"version": "0.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.6.0.tgz", "integrity": "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==", "dev": true}}}, "read-pkg-up": {"version": "7.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "integrity": "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==", "dev": true, "requires": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}}, "readable-stream": {"version": "3.6.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-3.6.1.tgz", "integrity": "sha512-+rQmrWMYGA90yenhTYsLWAsLsqVC8osOw6PKE1HDYiO0gdPeKe/xDHNzIAIn4C91YQ6oenEhfYqqc1883qHbjQ==", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "readdirp": {"version": "3.6.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "requires": {"picomatch": "^2.2.1"}}, "redent": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/redent/-/redent-3.0.0.tgz", "integrity": "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==", "dev": true, "requires": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}}, "regenerate": {"version": "1.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regenerate/-/regenerate-1.4.2.tgz", "integrity": "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==", "dev": true}, "regenerate-unicode-properties": {"version": "10.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz", "integrity": "sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==", "dev": true, "requires": {"regenerate": "^1.4.2"}}, "regenerator-runtime": {"version": "0.13.11", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "integrity": "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==", "dev": true}, "regenerator-transform": {"version": "0.15.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regenerator-transform/-/regenerator-transform-0.15.1.tgz", "integrity": "sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg==", "dev": true, "requires": {"@babel/runtime": "^7.8.4"}}, "regex-not": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "dependencies": {"safe-regex": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg==", "dev": true, "requires": {"ret": "~0.1.10"}}}}, "regexp-tree": {"version": "0.1.24", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regexp-tree/-/regexp-tree-0.1.24.tgz", "integrity": "sha512-s2aEVuLhvnVJW6s/iPgEGK6R+/xngd2jNQ+xy4bXNDKxZKJH6jpPHY6kVeVv1IeLCHgswRj+Kl3ELaDjG6V1iw==", "dev": true}, "regexp.prototype.flags": {"version": "1.4.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz", "integrity": "sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "functions-have-names": "^1.2.2"}}, "regexpp": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regexpp/-/regexpp-3.2.0.tgz", "integrity": "sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==", "dev": true}, "regexpu-core": {"version": "5.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regexpu-core/-/regexpu-core-5.3.1.tgz", "integrity": "sha512-nCOzW2V/X15XpLsK2rlgdwrysrBq+AauCn+omItIz4R1pIcmeot5zvjdmOBRLzEH/CkC6IxMJVmxDe3QcMuNVQ==", "dev": true, "requires": {"@babel/regjsgen": "^0.8.0", "regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.1.0", "regjsparser": "^0.9.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}}, "regjsparser": {"version": "0.9.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/regjsparser/-/regjsparser-0.9.1.tgz", "integrity": "sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==", "dev": true, "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==", "dev": true}}}, "remark": {"version": "13.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/remark/-/remark-13.0.0.tgz", "integrity": "sha512-HDz1+IKGtOyWN+QgBiAT0kn+2s6ovOxHyPAFGKVE81VSzJ+mq7RwHFledEvB5F1p4iJvOah/LOKdFuzvRnNLCA==", "dev": true, "requires": {"remark-parse": "^9.0.0", "remark-stringify": "^9.0.0", "unified": "^9.1.0"}}, "remark-parse": {"version": "9.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/remark-parse/-/remark-parse-9.0.0.tgz", "integrity": "sha512-geKatMwSzEXKHuzBNU1z676sGcDcFoChMK38TgdHJNAYfFtsfHDQG7MoJAjs6sgYMqyLduCYWDIWZIxiPeafEw==", "dev": true, "requires": {"mdast-util-from-markdown": "^0.8.0"}}, "remark-stringify": {"version": "9.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/remark-stringify/-/remark-stringify-9.0.1.tgz", "integrity": "sha512-mWmNg3ZtESvZS8fv5PTvaPckdL4iNlCHTt8/e/8oN08nArHRHjNZMKzA/YW3+p7/lYqIw4nx1XsjCBo/AxNChg==", "dev": true, "requires": {"mdast-util-to-markdown": "^0.6.0"}}, "repeat-element": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/repeat-element/-/repeat-element-1.1.4.tgz", "integrity": "sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ==", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==", "dev": true}, "replace-ext": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha512-vuNYXC7gG7IeVNBC1xUllqCcZKRbJoSPOBhnTEcAIiKCsbuef6zO3F0Rve3isPMMoNoQRWjQwbAgAjHUHniyEA==", "dev": true}, "require-directory": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="}, "require-from-string": {"version": "2.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "dev": true}, "reserved-words": {"version": "0.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/reserved-words/-/reserved-words-0.1.2.tgz", "integrity": "sha512-0S5SrIUJ9LfpbVl4Yzij6VipUdafHrOTzvmfazSw/jeZrZtQK303OPZW+obtkaw7jQlTQppy0UvZWm9872PbRw==", "dev": true}, "resolve": {"version": "1.22.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve/-/resolve-1.22.1.tgz", "integrity": "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==", "dev": true, "requires": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-cwd": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==", "dev": true, "requires": {"resolve-from": "^5.0.0"}, "dependencies": {"resolve-from": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true}}}, "resolve-from": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dev": true}, "resolve-url": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==", "dev": true}, "restore-cursor": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/restore-cursor/-/restore-cursor-3.1.0.tgz", "integrity": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==", "dev": true, "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "ret": {"version": "0.1.15", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ret/-/ret-0.1.15.tgz", "integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==", "dev": true}, "reusify": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true}, "rimraf": {"version": "2.6.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/rimraf/-/rimraf-2.6.3.tgz", "integrity": "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==", "dev": true, "requires": {"glob": "^7.1.3"}}, "run-async": {"version": "2.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/run-async/-/run-async-2.4.1.tgz", "integrity": "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==", "dev": true}, "run-parallel": {"version": "1.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "rxjs": {"version": "6.6.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/rxjs/-/rxjs-6.6.7.tgz", "integrity": "sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==", "dev": true, "requires": {"tslib": "^1.9.0"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "dev": true}}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true}, "safe-regex": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safe-regex/-/safe-regex-2.1.1.tgz", "integrity": "sha512-rx+x8AMzKb5Q5lQ95Zoi6ZbJqwCLkqi3XuJXp5P3rT8OEc6sZCJG5AE5dU3lsgRr/F4Bs31jSlVN+j5KrsGu9A==", "dev": true, "requires": {"regexp-tree": "~0.1.1"}}, "safe-regex-test": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safe-regex-test/-/safe-regex-test-1.0.0.tgz", "integrity": "sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "schema-utils": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/schema-utils/-/schema-utils-3.1.1.tgz", "integrity": "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==", "requires": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}}, "select": {"version": "1.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/select/-/select-1.1.2.tgz", "integrity": "sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA=="}, "semver": {"version": "7.3.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/semver/-/semver-7.3.8.tgz", "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "set-value": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/set-value/-/set-value-2.0.1.tgz", "integrity": "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "dev": true, "requires": {"isobject": "^3.0.1"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="}, "shebang-command": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="}, "side-channel": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "dev": true, "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true}, "signale": {"version": "1.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/signale/-/signale-1.4.0.tgz", "integrity": "sha512-iuh+gPf28RkltuJC7W5MRi6XAjTDCAPC/prJUpQoG4vIP3MJZ+GTydVnodXA7pwvTKb2cA0m9OFZW/cdWy/I/w==", "requires": {"chalk": "^2.3.2", "figures": "^2.0.0", "pkg-conf": "^2.1.0"}, "dependencies": {"chalk": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "figures": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/figures/-/figures-2.0.0.tgz", "integrity": "sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==", "requires": {"escape-string-regexp": "^1.0.5"}}}}, "slash": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true}, "slice-ansi": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/slice-ansi/-/slice-ansi-2.1.0.tgz", "integrity": "sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==", "dev": true, "requires": {"ansi-styles": "^3.2.0", "astral-regex": "^1.0.0", "is-fullwidth-code-point": "^2.0.0"}, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==", "dev": true}}}, "snapdragon": {"version": "0.8.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha512-Rr7<PERSON>jQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-map/-/source-map-0.5.7.tgz", "integrity": "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==", "dev": true}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-1.0.0.tgz", "integrity": "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "sortablejs": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/sortablejs/-/sortablejs-1.10.2.tgz", "integrity": "sha512-YkPGufevysvfwn5rfdlGyrGjt7/CRHwvRPogD/lC+TnvcN29jDpCifKP+rBqf+LRldfXSTh+0CGLcSg0VIxq3A=="}, "source-list-map": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-list-map/-/source-list-map-2.0.1.tgz", "integrity": "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}, "source-map-js": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-map-js/-/source-map-js-1.0.2.tgz", "integrity": "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="}, "source-map-resolve": {"version": "0.5.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "integrity": "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==", "dev": true, "requires": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-url": {"version": "0.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/source-map-url/-/source-map-url-0.4.1.tgz", "integrity": "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==", "dev": true}, "spdx-correct": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/spdx-correct/-/spdx-correct-3.1.1.tgz", "integrity": "sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz", "integrity": "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "integrity": "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.12", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/spdx-license-ids/-/spdx-license-ids-3.0.12.tgz", "integrity": "sha512-rr+VVSXtRhO4OHbXUiAF7xW3Bo9DuuF6C5jH+q/x15j2jniycgKbxU09Hr0WqlSLUs4i4ltHGXqTe7VHclYWyA==", "dev": true}, "specificity": {"version": "0.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/specificity/-/specificity-0.4.1.tgz", "integrity": "sha512-1klA3Gi5PD1Wv9Q0wUoOQN1IWAuPu0D1U03ThXTr0cJ20+/iq2tHSDnK7Kk/0LXJ1ztUB2/1Os0wKmfyNgUQfg==", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/split-string/-/split-string-3.1.0.tgz", "integrity": "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "split2": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/split2/-/split2-0.2.1.tgz", "integrity": "sha512-D/oTExYAkC9nWleOCTOyNmAuzfAT/6rHGBA9LIK7FVnGo13CSvrKCUzKenwH6U1s2znY9MqH6v0UQTEDa3vJmg==", "dev": true, "requires": {"through2": "~0.6.1"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "dev": true}, "through2": {"version": "0.6.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/through2/-/through2-0.6.5.tgz", "integrity": "sha512-RkK/CCESdTKQZHdmKICijdKKsCRVHs5KsLZ6pACAmF/1GPUQhonHSXWNERctxEp7RmvjdNbZTL5z9V7nSCXKcg==", "dev": true, "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "sprintf-js": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==", "dev": true}, "sql-formatter": {"version": "2.3.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/sql-formatter/-/sql-formatter-2.3.4.tgz", "integrity": "sha512-CajWtvzYoBJbD5PQeVe3E7AOHAIYvRQEPOKgF9kfKNeY8jtjBiiA6pDzkMuAID8jJMluoPvyKveLigSaA5tKQQ==", "requires": {"lodash": "^4.17.20"}}, "ssf": {"version": "0.10.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ssf/-/ssf-0.10.3.tgz", "integrity": "sha512-pRuUdW0WwyB2doSqqjWyzwCD6PkfxpHAHdZp39K3dp/Hq7f+xfMwNAWIi16DyrRg4gg9c/RvLYkJTSawTPTm1w==", "requires": {"frac": "~1.1.2"}}, "ssr-window": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ssr-window/-/ssr-window-3.0.0.tgz", "integrity": "sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA=="}, "state-toggle": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/state-toggle/-/state-toggle-1.0.3.tgz", "integrity": "sha512-d/5Z4/2iiCnHw6Xzghyhb+GcmF89bxwgXG60wjIiZaxnymbyOmI8Hk4VqHXiVVp6u2ysaskFfXg3ekCj4WNftQ==", "dev": true}, "static-extend": {"version": "0.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g==", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha512-Rr7<PERSON>jQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "string-width": {"version": "4.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "string.prototype.trimend": {"version": "1.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz", "integrity": "sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "string.prototype.trimstart": {"version": "1.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string.prototype.trimstart/-/string.prototype.trimstart-1.0.6.tgz", "integrity": "sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "string_decoder": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dev": true, "requires": {"safe-buffer": "~5.2.0"}}, "stringify-entities": {"version": "1.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stringify-entities/-/stringify-entities-1.3.2.tgz", "integrity": "sha512-nrBAQClJAPN2p+uGCVJRPIPakKeKWZ9GtBCmormE7pWOSlHat7+x5A8gx85M7HM5Dt0BP3pP5RhVW77WdbJJ3A==", "dev": true, "requires": {"character-entities-html4": "^1.0.0", "character-entities-legacy": "^1.0.0", "is-alphanumerical": "^1.0.0", "is-hexadecimal": "^1.0.0"}}, "strip-ansi": {"version": "6.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA=="}, "strip-indent": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-indent/-/strip-indent-3.0.0.tgz", "integrity": "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==", "dev": true, "requires": {"min-indent": "^1.0.0"}}, "strip-json-comments": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "dev": true}, "style-search": {"version": "0.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/style-search/-/style-search-0.1.0.tgz", "integrity": "sha512-Dj1Okke1C3uKKwQcetra4jSuk0DqbzbYtXipzFlFMZtowbF1x7BKJwB9AayVMyFARvU8EDrZdcax4At/452cAg==", "dev": true}, "stylelint": {"version": "13.7.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint/-/stylelint-13.7.0.tgz", "integrity": "sha512-1wStd4zVetnlHO98VjcHQbjSDmvcA39smkZQMct2cf+hom40H0xlQNdzzbswoG/jGBh61/Ue9m7Lu99PY51O6A==", "dev": true, "requires": {"@stylelint/postcss-css-in-js": "^0.37.2", "@stylelint/postcss-markdown": "^0.36.1", "autoprefixer": "^9.8.6", "balanced-match": "^1.0.0", "chalk": "^4.1.0", "cosmiconfig": "^7.0.0", "debug": "^4.1.1", "execall": "^2.0.0", "fast-glob": "^3.2.4", "fastest-levenshtein": "^1.0.12", "file-entry-cache": "^5.0.1", "get-stdin": "^8.0.0", "global-modules": "^2.0.0", "globby": "^11.0.1", "globjoin": "^0.1.4", "html-tags": "^3.1.0", "ignore": "^5.1.8", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "known-css-properties": "^0.19.0", "lodash": "^4.17.20", "log-symbols": "^4.0.0", "mathml-tag-names": "^2.1.3", "meow": "^7.1.1", "micromatch": "^4.0.2", "normalize-selector": "^0.2.0", "postcss": "^7.0.32", "postcss-html": "^0.36.0", "postcss-less": "^3.1.4", "postcss-media-query-parser": "^0.2.3", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^4.0.2", "postcss-sass": "^0.4.4", "postcss-scss": "^2.1.1", "postcss-selector-parser": "^6.0.2", "postcss-syntax": "^0.36.2", "postcss-value-parser": "^4.1.0", "resolve-from": "^5.0.0", "slash": "^3.0.0", "specificity": "^0.4.1", "string-width": "^4.2.0", "strip-ansi": "^6.0.0", "style-search": "^0.1.0", "sugarss": "^2.0.0", "svg-tags": "^1.0.0", "table": "^6.0.1", "v8-compile-cache": "^2.1.1", "write-file-atomic": "^3.0.3"}, "dependencies": {"html-tags": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/html-tags/-/html-tags-3.2.0.tgz", "integrity": "sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg==", "dev": true}, "ignore": {"version": "5.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-5.2.4.tgz", "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "dev": true}, "log-symbols": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "dev": true, "requires": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}}, "picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}, "resolve-from": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true}}}, "stylelint-config-css-modules": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-config-css-modules/-/stylelint-config-css-modules-2.3.0.tgz", "integrity": "sha512-nSxwaJMv9wBrTAi+O4qXubyi1AR9eB36tJpY0uaFhKgEc3fwWGUzUK1Edl8AQHAoU7wmUeKtsuYjblyRP/V7rw==", "dev": true}, "stylelint-config-prettier": {"version": "8.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-config-prettier/-/stylelint-config-prettier-8.0.2.tgz", "integrity": "sha512-TN1l93iVTXpF9NJstlvP7nOu9zY2k+mN0NSFQ/VEGz15ZIP9ohdDZTtCWHs5LjctAhSAzaILULGbgiM0ItId3A==", "dev": true}, "stylelint-config-rational-order": {"version": "0.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-config-rational-order/-/stylelint-config-rational-order-0.1.2.tgz", "integrity": "sha512-Qo7ZQaihCwTqijfZg4sbdQQHtugOX/B1/fYh018EiDZHW+lkqH9uHOnsDwDPGZrYJuB6CoyI7MZh2ecw2dOkew==", "dev": true, "requires": {"stylelint": "^9.10.1", "stylelint-order": "^2.2.1"}, "dependencies": {"@nodelib/fs.stat": {"version": "1.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz", "integrity": "sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw==", "dev": true}, "ansi-regex": {"version": "4.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-regex/-/ansi-regex-4.1.1.tgz", "integrity": "sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==", "dev": true}, "array-union": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/array-union/-/array-union-1.0.2.tgz", "integrity": "sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng==", "dev": true, "requires": {"array-uniq": "^1.0.1"}}, "arrify": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/arrify/-/arrify-1.0.1.tgz", "integrity": "sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==", "dev": true}, "braces": {"version": "2.3.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/braces/-/braces-2.3.2.tgz", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "camelcase": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/camelcase/-/camelcase-4.1.0.tgz", "integrity": "sha512-FxAv7HpHrXbh3aPo4o2qxHay2lkLY3x5Mw3KeE4KQE8ysVfziWeRZDwcjauvwBSGEC/nXUPzZy8zeh4HokqOnw==", "dev": true}, "camelcase-keys": {"version": "4.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/camelcase-keys/-/camelcase-keys-4.2.0.tgz", "integrity": "sha512-Ej37YKYbFUI8QiYlvj9YHb6/Z60dZyPJW0Cs8sFilMbd2lP0bw3ylAq9yJkK4lcTA2dID5fG8LjmJYbO7kWb7Q==", "dev": true, "requires": {"camelcase": "^4.1.0", "map-obj": "^2.0.0", "quick-lru": "^1.0.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "clone-regexp": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/clone-regexp/-/clone-regexp-1.0.1.tgz", "integrity": "sha512-Fcij9IwRW27XedRIJnSOEupS7RVcXtObJXbcUOX93UCLqqOdRpkvzKywOOSizmEK/Is3S/RHX9dLdfo6R1Q1mw==", "dev": true, "requires": {"is-regexp": "^1.0.0", "is-supported-regexp-flag": "^1.0.0"}}, "cosmiconfig": {"version": "5.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "integrity": "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==", "dev": true, "requires": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}}, "dir-glob": {"version": "2.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/dir-glob/-/dir-glob-2.2.2.tgz", "integrity": "sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw==", "dev": true, "requires": {"path-type": "^3.0.0"}}, "emoji-regex": {"version": "7.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/emoji-regex/-/emoji-regex-7.0.3.tgz", "integrity": "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==", "dev": true}, "execall": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/execall/-/execall-1.0.0.tgz", "integrity": "sha512-/J0Q8CvOvlAdpvhfkD/WnTQ4H1eU0exze2nFGPj/RSC7jpQ0NkKe2r28T5eMkhEEs+fzepMZNy1kVRKNlC04nQ==", "dev": true, "requires": {"clone-regexp": "^1.0.0"}}, "fast-glob": {"version": "2.2.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fast-glob/-/fast-glob-2.2.7.tgz", "integrity": "sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw==", "dev": true, "requires": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}}, "file-entry-cache": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/file-entry-cache/-/file-entry-cache-4.0.0.tgz", "integrity": "sha512-AVSwsnbV8vH/UVbvgEhf3saVQXORNv0ZzSkvkhQIaia5Tia+JhGTaa/ePUSVoPHQyGayQNmYfkzFi3WZV5zcpA==", "dev": true, "requires": {"flat-cache": "^2.0.1"}}, "fill-range": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ==", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "find-up": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/find-up/-/find-up-2.1.0.tgz", "integrity": "sha512-NWzkk0jSJtTt08+FBFMvXoeZnOJD+jTtsRmBYbAIzJdX6l7dLgR7CTubCM5/eDdPUBvLCeVasP1brfVR/9/EZQ==", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "get-stdin": {"version": "6.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/get-stdin/-/get-stdin-6.0.0.tgz", "integrity": "sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g==", "dev": true}, "glob-parent": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==", "dev": true, "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw==", "dev": true, "requires": {"is-extglob": "^2.1.0"}}}}, "globby": {"version": "9.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/globby/-/globby-9.2.0.tgz", "integrity": "sha512-ollPHROa5mcxDEkwg6bPt3QbEf4pDQSNtd6JPL1YvOvAo/7/0VAm9TccUeoTmarjPw4pfUthSCqcyfNB1I3ZSg==", "dev": true, "requires": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.2", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}, "dependencies": {"ignore": {"version": "4.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-4.0.6.tgz", "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==", "dev": true}}}, "ignore": {"version": "5.2.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ignore/-/ignore-5.2.4.tgz", "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "dev": true}, "import-fresh": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/import-fresh/-/import-fresh-2.0.0.tgz", "integrity": "sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg==", "dev": true, "requires": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "dependencies": {"resolve-from": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw==", "dev": true}}}, "import-lazy": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/import-lazy/-/import-lazy-3.1.0.tgz", "integrity": "sha512-8/gvXvX2JMn0F+CDlSC4l6kOmVaLOO3XLkksI7CI3Ud95KDYJuYur2b9P/PUt/i/pDAMd/DulQsNbbbmRRsDIQ==", "dev": true}, "indent-string": {"version": "3.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/indent-string/-/indent-string-3.2.0.tgz", "integrity": "sha512-BYqTHXTGUIvg7t1r4sJNKcbDZkL92nkXA8YtRpbjFHRHGDL/NtUeiBJMeE60kIFN/Mg8ESaWQvftaYMGJzQZCQ==", "dev": true}, "is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==", "dev": true}, "is-number": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-number/-/is-number-3.0.0.tgz", "integrity": "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-plain-obj": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "integrity": "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==", "dev": true}, "is-regexp": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-regexp/-/is-regexp-1.0.0.tgz", "integrity": "sha512-7zjFAPO4/gwyQAAgRRmqeEeyIICSdmCqa3tsVHMdBzaXXRiqopZL4Cyghg/XulGWrtABTpbnYYzzIRffLkP4oA==", "dev": true}, "known-css-properties": {"version": "0.11.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/known-css-properties/-/known-css-properties-0.11.0.tgz", "integrity": "sha512-bEZlJzXo5V/ApNNa5z375mJC6Nrz4vG43UgcSCrg2OHC+yuB6j0iDSrY7RQ/+PRofFB03wNIIt9iXIVLr4wc7w==", "dev": true}, "locate-path": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha512-NCI2kiDkyR7VeEKm27Kda/iQHyKJe1Bu0FlTbYp3CqJu+9IFe9bLyAjMxf5ZDDbEg+iMPzB5zYyUTSm8wVTKmA==", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "log-symbols": {"version": "2.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/log-symbols/-/log-symbols-2.2.0.tgz", "integrity": "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==", "dev": true, "requires": {"chalk": "^2.0.1"}}, "map-obj": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/map-obj/-/map-obj-2.0.0.tgz", "integrity": "sha512-TzQSV2DiMYgoF5RycneKVUzIa9bQsj/B3tTgsE3dOGqlzHnGIDaC7XBE7grnA+8kZPnfqSGFe95VHc2oc0VFUQ==", "dev": true}, "meow": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/meow/-/meow-5.0.0.tgz", "integrity": "sha512-CbTqYU17ABaLefO8vCU153ZZlprKYWDljcndKKDCFcYQITzWCXZAVk4QMFZPgvzrnUQ3uItnIE/LoUOwrT15Ig==", "dev": true, "requires": {"camelcase-keys": "^4.0.0", "decamelize-keys": "^1.0.0", "loud-rejection": "^1.0.0", "minimist-options": "^3.0.1", "normalize-package-data": "^2.3.4", "read-pkg-up": "^3.0.0", "redent": "^2.0.0", "trim-newlines": "^2.0.0", "yargs-parser": "^10.0.0"}}, "micromatch": {"version": "3.1.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "minimist-options": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/minimist-options/-/minimist-options-3.0.2.tgz", "integrity": "sha512-FyBrT/d0d4+uiZRbqznPXqw3IpZZG3gl3wKWiX784FycUKVwBt0uLBFkQrtE4tZOrgo78nZp2jnKz3L65T5LdQ==", "dev": true, "requires": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0"}}, "p-limit": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-limit/-/p-limit-1.3.0.tgz", "integrity": "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-locate/-/p-locate-2.0.0.tgz", "integrity": "sha512-nQja7m7gSKuewoVRen45CtVfODR3crN3goVQ0DDZ9N3yHxgpkuBhZqsaiotSQRrADUrne346peY7kT3TSACykg==", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/p-try/-/p-try-1.0.0.tgz", "integrity": "sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww==", "dev": true}, "parse-json": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==", "dev": true}, "path-type": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/path-type/-/path-type-3.0.0.tgz", "integrity": "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==", "dev": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/pify/-/pify-3.0.0.tgz", "integrity": "sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==", "dev": true}}}, "picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}, "postcss-sass": {"version": "0.3.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-sass/-/postcss-sass-0.3.5.tgz", "integrity": "sha512-B5z2Kob4xBxFjcufFnhQ2HqJQ2y/Zs/ic5EZbCywCkxKd756Q40cIQ/veRDwSrw1BF6+4wUgmpm0sBASqVi65A==", "dev": true, "requires": {"gonzales-pe": "^4.2.3", "postcss": "^7.0.1"}}, "postcss-selector-parser": {"version": "3.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "integrity": "sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA==", "dev": true, "requires": {"dot-prop": "^5.2.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}, "postcss-sorting": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-sorting/-/postcss-sorting-4.1.0.tgz", "integrity": "sha512-r4T2oQd1giURJdHQ/RMb72dKZCuLOdWx2B/XhXN1Y1ZdnwXsKH896Qz6vD4tFy9xSjpKNYhlZoJmWyhH/7JUQw==", "dev": true, "requires": {"lodash": "^4.17.4", "postcss": "^7.0.0"}}, "postcss-value-parser": {"version": "3.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==", "dev": true}, "quick-lru": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/quick-lru/-/quick-lru-1.1.0.tgz", "integrity": "sha512-tRS7sTgyxMXtLum8L65daJnHUhfDUgboRdcWW2bR9vBfrj2+O5HSMbQOJfJJjIVSPFqbBCF37FpwWXGitDc5tA==", "dev": true}, "read-pkg": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/read-pkg/-/read-pkg-3.0.0.tgz", "integrity": "sha512-BLq/cCO9two+lBgiTYNqD6GdtK8s4NpaWrl6/rCO9w0TUS8oJl7cmToOZfRYllKTISY6nt1U7jQ53brmKqY6BA==", "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}}, "read-pkg-up": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/read-pkg-up/-/read-pkg-up-3.0.0.tgz", "integrity": "sha512-YFzFrVvpC6frF1sz8psoHDBGF7fLPc+llq/8NB43oagqWkx8ar5zYtsTORtOjw9W2RHLpWP+zTWwBvf1bCmcSw==", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^3.0.0"}}, "redent": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/redent/-/redent-2.0.0.tgz", "integrity": "sha512-XNwrTx77JQCEMXTeb8movBKuK75MgH0RZkujNuDKCezemx/voapl9i2gCSi8WWm8+ox5ycJi1gxF22fR7c0Ciw==", "dev": true, "requires": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}}, "slash": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/slash/-/slash-2.0.0.tgz", "integrity": "sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==", "dev": true}, "string-width": {"version": "3.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/string-width/-/string-width-3.1.0.tgz", "integrity": "sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "strip-ansi": {"version": "5.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}, "strip-indent": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/strip-indent/-/strip-indent-2.0.0.tgz", "integrity": "sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA==", "dev": true}, "stylelint": {"version": "9.10.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint/-/stylelint-9.10.1.tgz", "integrity": "sha512-9UiHxZhOAHEgeQ7oLGwrwoDR8vclBKlSX7r4fH0iuu0SfPwFaLkb1c7Q2j1cqg9P7IDXeAV2TvQML/fRQzGBBQ==", "dev": true, "requires": {"autoprefixer": "^9.0.0", "balanced-match": "^1.0.0", "chalk": "^2.4.1", "cosmiconfig": "^5.0.0", "debug": "^4.0.0", "execall": "^1.0.0", "file-entry-cache": "^4.0.0", "get-stdin": "^6.0.0", "global-modules": "^2.0.0", "globby": "^9.0.0", "globjoin": "^0.1.4", "html-tags": "^2.0.0", "ignore": "^5.0.4", "import-lazy": "^3.1.0", "imurmurhash": "^0.1.4", "known-css-properties": "^0.11.0", "leven": "^2.1.0", "lodash": "^4.17.4", "log-symbols": "^2.0.0", "mathml-tag-names": "^2.0.1", "meow": "^5.0.0", "micromatch": "^3.1.10", "normalize-selector": "^0.2.0", "pify": "^4.0.0", "postcss": "^7.0.13", "postcss-html": "^0.36.0", "postcss-jsx": "^0.36.0", "postcss-less": "^3.1.0", "postcss-markdown": "^0.36.0", "postcss-media-query-parser": "^0.2.3", "postcss-reporter": "^6.0.0", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^4.0.0", "postcss-sass": "^0.3.5", "postcss-scss": "^2.0.0", "postcss-selector-parser": "^3.1.0", "postcss-syntax": "^0.36.2", "postcss-value-parser": "^3.3.0", "resolve-from": "^4.0.0", "signal-exit": "^3.0.2", "slash": "^2.0.0", "specificity": "^0.4.1", "string-width": "^3.0.0", "style-search": "^0.1.0", "sugarss": "^2.0.0", "svg-tags": "^1.0.0", "table": "^5.0.0"}}, "stylelint-order": {"version": "2.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-order/-/stylelint-order-2.2.1.tgz", "integrity": "sha512-019KBV9j8qp1MfBjJuotse6MgaZqGVtXMc91GU9MsS9Feb+jYUvUU3Z8XiClqPdqJZQ0ryXQJGg3U3PcEjXwfg==", "dev": true, "requires": {"lodash": "^4.17.10", "postcss": "^7.0.2", "postcss-sorting": "^4.1.0"}}, "table": {"version": "5.4.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/table/-/table-5.4.6.tgz", "integrity": "sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==", "dev": true, "requires": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "trim-newlines": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/trim-newlines/-/trim-newlines-2.0.0.tgz", "integrity": "sha512-MTBWv3jhVjTU7XR3IQHllbiJs8sc75a80OEhB6or/q7pLTWgQ0bMGQXXYQSrSuXe6WiKWDZ5txXY5P59a/coVA==", "dev": true}, "yargs-parser": {"version": "10.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs-parser/-/yargs-parser-10.1.0.tgz", "integrity": "sha512-VCIyR1wJoEBZUqk5PA+oOBF6ypbwh5aNB3I50guxAL/quggdfs4TtNHQrSazFA3fYZ+tEqfs0zIGlv0c/rgjbQ==", "dev": true, "requires": {"camelcase": "^4.1.0"}}}}, "stylelint-config-recommended": {"version": "5.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-config-recommended/-/stylelint-config-recommended-5.0.0.tgz", "integrity": "sha512-c8aubuARSu5A3vEHLBeOSJt1udOdS+1iue7BmJDTSXoCBmfEQmmWX+59vYIj3NQdJBY6a/QRv1ozVFpaB9jaqA==", "dev": true}, "stylelint-config-standard": {"version": "22.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-config-standard/-/stylelint-config-standard-22.0.0.tgz", "integrity": "sha512-uQVNi87SHjqTm8+4NIP5NMAyY/arXrBgimaaT7skvRfE9u3JKXRK9KBkbr4pVmeciuCcs64kAdjlxfq6Rur7Hw==", "dev": true, "requires": {"stylelint-config-recommended": "^5.0.0"}}, "stylelint-declaration-block-no-ignored-properties": {"version": "2.7.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-declaration-block-no-ignored-properties/-/stylelint-declaration-block-no-ignored-properties-2.7.0.tgz", "integrity": "sha512-44SpI9+9Oc1ICuwwRfwS/3npQ2jPobDSTnwWdNgZGryGqQCp17CgEIWjCv1BgUOSzND3RqywNCNLKvO1AOxbfg==", "dev": true}, "stylelint-no-unsupported-browser-features": {"version": "5.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-no-unsupported-browser-features/-/stylelint-no-unsupported-browser-features-5.0.4.tgz", "integrity": "sha512-05DeIsWv262DXTSWhbQURDOxno9Tsu81sNnOpG1gD39WT+NNsEjhR2BQvwvNpZ/J/lS8vHxjhHULU2OGKSswvA==", "dev": true, "requires": {"doiuse": "^4.4.1", "lodash": "^4.17.15", "postcss": "^8.4.16"}}, "stylelint-order": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-order/-/stylelint-order-4.1.0.tgz", "integrity": "sha512-sVTikaDvMqg2aJjh4r48jsdfmqLT+nqB1MOsaBnvM3OwLx4S+WXcsxsgk5w18h/OZoxZCxuyXMh61iBHcj9Qiw==", "dev": true, "requires": {"lodash": "^4.17.15", "postcss": "^7.0.31", "postcss-sorting": "^5.0.1"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "stylelint-z-index-value-constraint": {"version": "1.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/stylelint-z-index-value-constraint/-/stylelint-z-index-value-constraint-1.3.0.tgz", "integrity": "sha512-KS1/YPx01uD4dqQmmdDdPSgdsDEdgPMPhfAIgOmnK7dAo1q/9HincEG2iU/lrWbzvIWyHHdo3ZkhKeex0GAlaQ==", "dev": true}, "sugarss": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/sugarss/-/sugarss-2.0.0.tgz", "integrity": "sha512-WfxjozUk0UVA4jm+U1d736AUpzSrNsQcIbyOkoE364GrtWmIrFdk5lksEupgWMD4VaT/0kVx1dobpiDumSgmJQ==", "dev": true, "requires": {"postcss": "^7.0.2"}, "dependencies": {"picocolors": {"version": "0.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "dev": true}, "postcss": {"version": "7.0.39", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/postcss/-/postcss-7.0.39.tgz", "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "dev": true, "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}}}, "supports-color": {"version": "5.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "requires": {"has-flag": "^3.0.0"}}, "supports-hyperlinks": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz", "integrity": "sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==", "dev": true, "requires": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true}, "svg-tags": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/svg-tags/-/svg-tags-1.0.0.tgz", "integrity": "sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==", "dev": true}, "swiper": {"version": "6.8.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/swiper/-/swiper-6.8.4.tgz", "integrity": "sha512-O+buF9Q+sMA0H7luMS8R59hCaJKlpo8PXhQ6ZYu6Rn2v9OsFd4d1jmrv14QvxtQpKAvL/ZiovEeANI/uDGet7g==", "requires": {"dom7": "^3.0.0", "ssr-window": "^3.0.0"}}, "table": {"version": "6.8.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/table/-/table-6.8.1.tgz", "integrity": "sha512-Y4X9zqrCftUhMeH2EptSSERdVKt/nEdijTOacGD/97EKjhQ/Qs8RTlEGABSJNNN8lac9kheH+af7yAkEWlgneA==", "dev": true, "requires": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ajv/-/ajv-8.12.0.tgz", "integrity": "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "astral-regex": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/astral-regex/-/astral-regex-2.0.0.tgz", "integrity": "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==", "dev": true}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "json-schema-traverse": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "dev": true}, "slice-ansi": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/slice-ansi/-/slice-ansi-4.0.0.tgz", "integrity": "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==", "dev": true, "requires": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}}}}, "text-table": {"version": "0.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/text-table/-/text-table-0.2.0.tgz", "integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==", "dev": true}, "through": {"version": "2.3.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/through/-/through-2.3.8.tgz", "integrity": "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==", "dev": true}, "through2": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/through2/-/through2-4.0.2.tgz", "integrity": "sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw==", "dev": true, "requires": {"readable-stream": "3"}}, "tiny-emitter": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tiny-emitter/-/tiny-emitter-2.1.0.tgz", "integrity": "sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q=="}, "tmp": {"version": "0.0.33", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "requires": {"os-tmpdir": "~1.0.2"}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA=="}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "dev": true}, "to-object-path": {"version": "0.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg==", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "dependencies": {"safe-regex": {"version": "1.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg==", "dev": true, "requires": {"ret": "~0.1.10"}}}}, "to-regex-range": {"version": "5.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "requires": {"is-number": "^7.0.0"}}, "trim": {"version": "0.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/trim/-/trim-0.0.1.tgz", "integrity": "sha512-YzQV+TZg4AxpKxaTHK3c3D+kRDCGVEE7LemdlQZoQXn0iennk10RsIoY6ikzAqJTc9Xjl9C1/waHom/J86ziAQ==", "dev": true}, "trim-newlines": {"version": "3.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/trim-newlines/-/trim-newlines-3.0.1.tgz", "integrity": "sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==", "dev": true}, "trim-trailing-lines": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/trim-trailing-lines/-/trim-trailing-lines-1.1.4.tgz", "integrity": "sha512-rjUWSqnfTNrjbB9NQWfPMH/xRK1deHeGsHoVfpxJ++XeYXE0d6B1En37AHfw3jtfTU7dzMzZL2jjpe8Qb5gLIQ==", "dev": true}, "trough": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/trough/-/trough-1.0.5.tgz", "integrity": "sha512-rvuRbTarPXmMb79SmzEp8aqXNKcK+y0XaB298IXueQ8I2PsrATcPBCSPyK/dDNa2iWOhKlfNnOjdAOTBU/nkFA==", "dev": true}, "tsconfig-paths": {"version": "3.14.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tsconfig-paths/-/tsconfig-paths-3.14.2.tgz", "integrity": "sha512-o/9iXgCYc5L/JxCHPe3Hvh8Q/2xm5Z+p18PESBU6Ff33695QnCHBEjcytY2q19ua7Mbl/DavtBOLq+oG0RCL+g==", "dev": true, "requires": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}, "dependencies": {"json5": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/json5/-/json5-1.0.2.tgz", "integrity": "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==", "dev": true, "requires": {"minimist": "^1.2.0"}}}}, "tslib": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tslib/-/tslib-2.3.0.tgz", "integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="}, "tsutils": {"version": "3.21.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tsutils/-/tsutils-3.21.0.tgz", "integrity": "sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==", "dev": true, "requires": {"tslib": "^1.8.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "dev": true}}}, "type-check": {"version": "0.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-check/-/type-check-0.4.0.tgz", "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "dev": true, "requires": {"prelude-ls": "^1.2.1"}}, "type-fest": {"version": "0.8.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/type-fest/-/type-fest-0.8.1.tgz", "integrity": "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==", "dev": true}, "typed-array-length": {"version": "1.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/typed-array-length/-/typed-array-length-1.0.4.tgz", "integrity": "sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==", "dev": true, "requires": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}}, "typedarray-to-buffer": {"version": "3.1.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==", "dev": true, "requires": {"is-typedarray": "^1.0.0"}}, "typescript": {"version": "4.9.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/typescript/-/typescript-4.9.5.tgz", "integrity": "sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==", "dev": true}, "unbox-primitive": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unbox-primitive/-/unbox-primitive-1.0.2.tgz", "integrity": "sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==", "dev": true, "requires": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}}, "underscore": {"version": "1.13.7", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/underscore/-/underscore-1.13.7.tgz", "integrity": "sha512-GMXzWtsc57XAtguZgaQViUOzs0KTkk8ojr3/xAxXLITqf/3EMwxC0inyETfDFjH/Krbhuep0HNbbjI9i/q3F3g=="}, "unherit": {"version": "1.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unherit/-/unherit-1.1.3.tgz", "integrity": "sha512-Ft16BJcnapDKp0+J/rqFC3Rrk6Y/Ng4nzsC028k2jdDII/rdZ7Wd3pPT/6+vIIxRagwRc9K0IUX0Ra4fKvw+WQ==", "dev": true, "requires": {"inherits": "^2.0.0", "xtend": "^4.0.0"}}, "unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "integrity": "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==", "dev": true}, "unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==", "dev": true, "requires": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}}, "unicode-match-property-value-ecmascript": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz", "integrity": "sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==", "dev": true}, "unicode-property-aliases-ecmascript": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "integrity": "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==", "dev": true}, "unified": {"version": "9.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unified/-/unified-9.2.2.tgz", "integrity": "sha512-Sg7j110mtefBD+qunSLO1lqOEKdrwBFBrR6Qd8f4uwkhWNlbkaqwHse6e7QvD3AP/MNoJdEDLaf8OxYyoWgorQ==", "dev": true, "requires": {"bail": "^1.0.0", "extend": "^3.0.0", "is-buffer": "^2.0.0", "is-plain-obj": "^2.0.0", "trough": "^1.0.0", "vfile": "^4.0.0"}}, "union-value": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/union-value/-/union-value-1.0.1.tgz", "integrity": "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "uniq": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/uniq/-/uniq-1.0.1.tgz", "integrity": "sha512-Gw+zz50YNKPDKXs+9d+aKAjVwpjNwqzvNpLigIruT4HA9lMZNdMqs9x07kKHB/L9WRzqp4+DlTU5s4wG2esdoA==", "dev": true}, "unist-util-find-all-after": {"version": "3.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-find-all-after/-/unist-util-find-all-after-3.0.2.tgz", "integrity": "sha512-xaTC/AGZ0rIM2gM28YVRAFPIZpzbpDtU3dRmp7EXlNVA8ziQc4hY3H7BHXM1J49nEmiqc3svnqMReW+PGqbZKQ==", "dev": true, "requires": {"unist-util-is": "^4.0.0"}}, "unist-util-is": {"version": "4.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-is/-/unist-util-is-4.1.0.tgz", "integrity": "sha512-ZOQSsnce92GrxSqlnEEseX0gi7GH9zTJZ0p9dtu87WRb/37mMPO2Ilx1s/t9vBHrFhbgweUwb+t7cIn5dxPhZg==", "dev": true}, "unist-util-remove-position": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-remove-position/-/unist-util-remove-position-1.1.4.tgz", "integrity": "sha512-tLqd653ArxJIPnKII6LMZwH+mb5q+n/GtXQZo6S6csPRs5zB0u79Yw8ouR3wTw8wxvdJFhpP6Y7jorWdCgLO0A==", "dev": true, "requires": {"unist-util-visit": "^1.1.0"}}, "unist-util-stringify-position": {"version": "2.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-stringify-position/-/unist-util-stringify-position-2.0.3.tgz", "integrity": "sha512-3faScn5I+hy9VleOq/qNbAd6pAx7iH5jYBMS9I1HgQVijz/4mv5Bvw5iw1sC/90CODiKo81G/ps8AJrISn687g==", "dev": true, "requires": {"@types/unist": "^2.0.2"}}, "unist-util-visit": {"version": "1.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-visit/-/unist-util-visit-1.4.1.tgz", "integrity": "sha512-AvGNk7Bb//EmJZyhtRUnNMEpId/AZ5Ph/KUpTI09WHQuDZHKovQ1oEv3mfmKpWKtoMzyMC4GLBm1Zy5k12fjIw==", "dev": true, "requires": {"unist-util-visit-parents": "^2.0.0"}}, "unist-util-visit-parents": {"version": "2.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-visit-parents/-/unist-util-visit-parents-2.1.2.tgz", "integrity": "sha512-DyN5vD4NE3aSeB+PXYNKxzGsfocxp6asDc2XXE3b0ekO2BaRUpBicbbUygfSvYfUz1IkmjFR1YF7dPklraMZ2g==", "dev": true, "requires": {"unist-util-is": "^3.0.0"}, "dependencies": {"unist-util-is": {"version": "3.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unist-util-is/-/unist-util-is-3.0.0.tgz", "integrity": "sha512-sVZZX3+kspVNmLWBPAB6r+7D9ZgAFPNWm66f7YNb420RlQSbn+n8rG8dGZSkrER7ZIXGQYNm5pqC3v3HopH24A==", "dev": true}}}, "universalify": {"version": "0.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/universalify/-/universalify-0.1.2.tgz", "integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="}, "unset-value": {"version": "1.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ==", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-value/-/has-value-0.3.1.tgz", "integrity": "sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q==", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/isobject/-/isobject-2.1.0.tgz", "integrity": "sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA==", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/has-values/-/has-values-0.1.4.tgz", "integrity": "sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ==", "dev": true}}}, "update-browserslist-db": {"version": "1.0.10", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "integrity": "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==", "dev": true, "requires": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}}, "uri-js": {"version": "4.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "requires": {"punycode": "^2.1.0"}}, "urix": {"version": "0.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/urix/-/urix-0.1.0.tgz", "integrity": "sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg==", "dev": true}, "use": {"version": "3.1.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/use/-/use-3.1.1.tgz", "integrity": "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==", "dev": true}, "util-deprecate": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "uuid": {"version": "9.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/uuid/-/uuid-9.0.0.tgz", "integrity": "sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg=="}, "v8-compile-cache": {"version": "2.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "integrity": "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==", "dev": true}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "integrity": "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "vfile": {"version": "4.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vfile/-/vfile-4.2.1.tgz", "integrity": "sha512-O6AE4OskCG5S1emQ/4gl8zK586RqA3srz3nfK/Viy0UPToBc5Trp9BVFb1u0CjsKrAWwnpr4ifM/KBXPWwJbCA==", "dev": true, "requires": {"@types/unist": "^2.0.0", "is-buffer": "^2.0.0", "unist-util-stringify-position": "^2.0.0", "vfile-message": "^2.0.0"}}, "vfile-location": {"version": "2.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vfile-location/-/vfile-location-2.0.6.tgz", "integrity": "sha512-sSFdyCP3G6Ka0CEmN83A2YCMKIieHx0EDaj5IDP4g1pa5ZJ4FJDvpO0WODLxo4LUX4oe52gmSCK7Jw4SBghqxA==", "dev": true}, "vfile-message": {"version": "2.0.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vfile-message/-/vfile-message-2.0.4.tgz", "integrity": "sha512-DjssxRGkMvifUOJre00juHoP9DPWuzjxKuMDrhNbk2TdaYYBNMStsNhEOt3idrtI12VQYM/1+iM0KOzXi4pxwQ==", "dev": true, "requires": {"@types/unist": "^2.0.0", "unist-util-stringify-position": "^2.0.0"}}, "vis": {"version": "4.21.0-EOL", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vis/-/vis-4.21.0-EOL.tgz", "integrity": "sha512-JVS1mywKg5S88XbkDJPfCb3n+vlg5fMA8Ae2hzs3KHAwD4ryM5qwlbFZ6ReDfY8te7I4NLCpuCoywJQEehvJlQ==", "requires": {"emitter-component": "^1.1.1", "hammerjs": "^2.0.8", "keycharm": "^0.2.0", "moment": "^2.18.1", "propagating-hammerjs": "^1.4.6"}}, "vue": {"version": "2.7.14", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue/-/vue-2.7.14.tgz", "integrity": "sha512-b2qkFyOM0kwqWFuQmgd4o+uHGU7T+2z3T+WQp8UBjADfEv2n4FEMffzBmCKNP0IGzOEEfYjvtcC62xaSKeQDrQ==", "requires": {"@vue/compiler-sfc": "2.7.14", "csstype": "^3.1.0"}}, "vue-codemirror": {"version": "4.0.6", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-codemirror/-/vue-codemirror-4.0.6.tgz", "integrity": "sha512-ilU7Uf0mqBNSSV3KT7FNEeRIxH4s1fmpG4TfHlzvXn0QiQAbkXS9lLfwuZpaBVEnpP5CSE62iGJjoliTuA8poQ==", "requires": {"codemirror": "^5.41.0", "diff-match-patch": "^1.0.0"}}, "vue-eslint-parser": {"version": "7.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-eslint-parser/-/vue-eslint-parser-7.1.0.tgz", "integrity": "sha512-Kr21uPfthDc63nDl27AGQEhtt9VrZ9nkYk/NTftJ2ws9XiJwzJJCnCr3AITQ2jpRMA0XPGDECxYH8E027qMK9Q==", "dev": true, "requires": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "dependencies": {"espree": {"version": "6.2.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/espree/-/espree-6.2.1.tgz", "integrity": "sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==", "dev": true, "requires": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}}}}, "vue-grid-layout": {"version": "2.4.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-grid-layout/-/vue-grid-layout-2.4.0.tgz", "integrity": "sha512-MRQVt1BdWDaPN4gKGEKOVVwiTyucqJhrGEyjiY9Muor+dzFFq4Hm0geSpYJpLvC1GLlTL8KWUwy0suKrHm+mqg==", "requires": {"@interactjs/actions": "1.10.2", "@interactjs/auto-scroll": "1.10.2", "@interactjs/auto-start": "1.10.2", "@interactjs/dev-tools": "1.10.2", "@interactjs/interactjs": "1.10.2", "@interactjs/modifiers": "1.10.2", "element-resize-detector": "^1.2.1"}}, "vue-i18n": {"version": "8.28.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-i18n/-/vue-i18n-8.28.2.tgz", "integrity": "sha512-C5GZjs1tYlAqjwymaaCPDjCyGo10ajUphiwA922jKt9n7KPpqR7oM1PCwYzhB/E7+nT3wfdG3oRre5raIT1rKA=="}, "vue-json-diff": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-json-diff/-/vue-json-diff-1.0.1.tgz", "integrity": "sha512-aRo9O4qsUcKtKV3k+Li3OvStFawzTluScvN5sN7doFYKu5+Gptrc2ErSpfW+0g4JPyqkJDaVWF55B/zY2pO9bA==", "requires": {"vue": "^2.6.12"}}, "vue-json-editor-fix-cn": {"version": "1.4.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-json-editor-fix-cn/-/vue-json-editor-fix-cn-1.4.3.tgz", "integrity": "sha512-MxnX+SuBEj3uLmh6Teh9Ui6TXK1eEpU1MWpDnJ5bFauIYOrcrQXmEby64hiWNairJMdADzoFS8HP/W1BBy0dEg==", "requires": {"vue": "^2.2.6"}}, "vue-json-viewer": {"version": "2.2.22", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-json-viewer/-/vue-json-viewer-2.2.22.tgz", "integrity": "sha512-3oPH5BxoUWva/qp7wNJj+15FBXyi9Yu5VDW4mCWivjHR1pUpMv34fjqqxML7jh2uOqm1S/3Xks5nQ5JjC5+OWw==", "requires": {"clipboard": "^2.0.4"}}, "vue-meditor": {"version": "2.1.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-meditor/-/vue-meditor-2.1.2.tgz", "integrity": "sha512-Jg4n1DUvMbVW9zoLonG/GnFOGgIFdt6P5uuJ7EkdCKWZkSiS0tjT6TpxJyeQji3pFb7h6YnDC/BGWs8UynbuPg=="}, "vue-router": {"version": "3.5.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-router/-/vue-router-3.5.0.tgz", "integrity": "sha512-QYrPzHMJiJCq20ezhlGok+NbrmjzhQDG6pnpJaD14Eg3NvT07s3acYz2ktxJ7vGHd/Ts4TgG9Tt8a2PA+Js5fw=="}, "vue-virtual-scroll-list": {"version": "2.3.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vue-virtual-scroll-list/-/vue-virtual-scroll-list-2.3.5.tgz", "integrity": "sha512-YFK6u5yltqtAOfTBcij/KGAS2SoZvzbNIAf9qTULauPObEp53xj22tDuohrrM2vNkgoD5kejXICIUBt2Q4ZDqQ=="}, "vuedraggable": {"version": "2.24.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/vuedraggable/-/vuedraggable-2.24.3.tgz", "integrity": "sha512-6/HDXi92GzB+Hcs9fC6PAAozK1RLt1ewPTLjK0anTYguXLAeySDmcnqE8IC0xa7shvSzRjQXq3/+dsZ7ETGF3g==", "requires": {"sortablejs": "1.10.2"}}, "wcwidth": {"version": "1.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/wcwidth/-/wcwidth-1.0.1.tgz", "integrity": "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==", "dev": true, "requires": {"defaults": "^1.0.3"}}, "webpack-merge": {"version": "4.2.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/webpack-merge/-/webpack-merge-4.2.2.tgz", "integrity": "sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==", "dev": true, "requires": {"lodash": "^4.17.15"}}, "webpack-sources": {"version": "1.4.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/webpack-sources/-/webpack-sources-1.4.3.tgz", "integrity": "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==", "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "which": {"version": "2.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "requires": {"isexe": "^2.0.0"}}, "which-boxed-primitive": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "integrity": "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==", "dev": true, "requires": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}}, "which-typed-array": {"version": "1.1.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/which-typed-array/-/which-typed-array-1.1.9.tgz", "integrity": "sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0", "is-typed-array": "^1.1.10"}}, "word-wrap": {"version": "1.2.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/word-wrap/-/word-wrap-1.2.3.tgz", "integrity": "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==", "dev": true}, "worker-loader": {"version": "3.0.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/worker-loader/-/worker-loader-3.0.8.tgz", "integrity": "sha512-XQyQkIFeRVC7f7uRhFdNMe/iJOdO6zxAaR3EWbDp45v3mDhrTi+++oswKNxShUNjPC/1xUp5DB29YKLhFo129g==", "dev": true, "requires": {"loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}}}, "wrappy": {"version": "1.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}, "write": {"version": "1.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/write/-/write-1.0.3.tgz", "integrity": "sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==", "dev": true, "requires": {"mkdirp": "^0.5.1"}}, "write-file-atomic": {"version": "3.0.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "integrity": "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==", "dev": true, "requires": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "x-is-string": {"version": "0.1.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/x-is-string/-/x-is-string-0.1.0.tgz", "integrity": "sha512-GojqklwG8gpzOVEVki5KudKNoq7MbbjYZCbyWzEz7tyPA7eleiE0+ePwOWQQRb5fm86rD3S8Tc0tSFf3AOv50w==", "dev": true}, "xlsx": {"version": "0.15.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/xlsx/-/xlsx-0.15.5.tgz", "integrity": "sha512-iWyTqe6UGTkp3XQOeeKPEBcZvmBfzIo3hDIVDfhGIEoTGVIq2JWEk6tIx0F+oKUje3pfZUx4V1W+P6892AB8kQ==", "requires": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}}, "xss": {"version": "1.0.15", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/xss/-/xss-1.0.15.tgz", "integrity": "sha512-FVdlVVC67WOIPvfOwhoMETV72f6GbW7aOabBC3WxN/oUdoEMDyLz4OgRv5/gck2ZeNqEQu+Tb0kloovXOfpYVg==", "requires": {"commander": "^2.20.3", "cssfilter": "0.0.10"}, "dependencies": {"commander": {"version": "2.20.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}}}, "xtend": {"version": "4.0.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/xtend/-/xtend-4.0.2.tgz", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==", "dev": true}, "y18n": {"version": "5.0.8", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="}, "yallist": {"version": "4.0.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "dev": true}, "yaml": {"version": "1.10.2", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yaml/-/yaml-1.10.2.tgz", "integrity": "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==", "dev": true}, "yargs": {"version": "16.2.0", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs/-/yargs-16.2.0.tgz", "integrity": "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==", "dev": true, "requires": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "dependencies": {"yargs-parser": {"version": "20.2.9", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "dev": true}}}, "yargs-parser": {"version": "18.1.3", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/yargs-parser/-/yargs-parser-18.1.3.tgz", "integrity": "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==", "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "zrender": {"version": "5.4.1", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/zrender/-/zrender-5.4.1.tgz", "integrity": "sha512-M4Z05BHWtajY2241EmMPHglDQAJ1UyHQcYsxDNzD9XLSkPDqMq4bB28v9Pb4mvHnVQ0GxyTklZ/69xCFP6RXBA==", "requires": {"tslib": "2.3.0"}}, "zwitch": {"version": "1.0.5", "resolved": "http://artifactory.hundsun.com:80/artifactory/api/npm/jresui2-npm-virtual/zwitch/-/zwitch-1.0.5.tgz", "integrity": "sha512-V50KMwwzqJV0NpZIZFwfOD5/lyny3WlSzRiXgA0G7VUnRlqttta1L6UQIHzd6EuBY/cHGfwTIck7w1yH6Q5zUw==", "dev": true}}}