const fs = require('fs');

// 批量翻译脚本
const translations = {
    // 基础词汇
    '查询': 'Query',
    '搜索': 'Search',
    '添加': 'Add',
    '删除': 'Delete',
    '修改': 'Modify',
    '编辑': 'Edit',
    '保存': 'Save',
    '取消': 'Cancel',
    '确认': 'Confirm',
    '提交': 'Submit',
    '重置': 'Reset',
    '刷新': 'Refresh',
    '导出': 'Export',
    '导入': 'Import',
    '上传': 'Upload',
    '下载': 'Download',
    '复制': 'Copy',
    '清空': 'Clear',
    '选择': 'Select',
    '全选': 'Select All',
    
    // 状态
    '成功': 'Success',
    '失败': 'Failed',
    '错误': 'Error',
    '警告': 'Warning',
    '信息': 'Info',
    '加载中': 'Loading',
    '运行中': 'Running',
    '已停止': 'Stopped',
    '已暂停': 'Paused',
    '已完成': 'Completed',
    '待处理': 'Pending',
    '处理中': 'Processing',
    '在线': 'Online',
    '离线': 'Offline',
    '启用': 'Enabled',
    '禁用': 'Disabled',
    '正常': 'Normal',
    '异常': 'Abnormal',
    
    // 业务术语
    '交易': 'Trading',
    '订单': 'Order',
    '委托': 'Order',
    '成交': 'Deal',
    '账户': 'Account',
    '资金': 'Fund',
    '证券': 'Securities',
    '期货': 'Futures',
    '期权': 'Options',
    '风控': 'Risk Control',
    '行情': 'Market Data',
    '报盘': 'Order Routing',
    '核心': 'Core',
    '网关': 'Gateway',
    '前置': 'Front-end',
    
    // 技术术语
    '时延': 'Latency',
    '延迟': 'Delay',
    '吞吐': 'Throughput',
    '性能': 'Performance',
    '链路': 'Link',
    '拓扑': 'Topology',
    '线程': 'Thread',
    '进程': 'Process',
    '队列': 'Queue',
    '缓存': 'Cache',
    '内存': 'Memory',
    '存储': 'Storage',
    '数据库': 'Database',
    '索引': 'Index',
    '分片': 'Shard',
    '同步': 'Sync',
    '备份': 'Backup',
    '恢复': 'Recovery',
    
    // 系统相关
    '系统': 'System',
    '应用': 'Application',
    '服务': 'Service',
    '节点': 'Node',
    '集群': 'Cluster',
    '网络': 'Network',
    '连接': 'Connection',
    '配置': 'Configuration',
    '设置': 'Settings',
    '管理': 'Management',
    '用户': 'User',
    '权限': 'Permission',
    '角色': 'Role',
    
    // 数据相关
    '数据': 'Data',
    '记录': 'Record',
    '条目': 'Item',
    '列表': 'List',
    '详情': 'Details',
    '统计': 'Statistics',
    '分析': 'Analysis',
    '报表': 'Report',
    '图表': 'Chart',
    '监控': 'Monitor',
    '观测': 'Observation',
    
    // 时间相关
    '今天': 'Today',
    '昨天': 'Yesterday',
    '本周': 'This Week',
    '本月': 'This Month',
    '开始时间': 'Start Time',
    '结束时间': 'End Time',
    '时间范围': 'Time Range',
    '最近': 'Recent',
    '历史': 'History',
    
    // 表单相关
    '请输入': 'Please enter',
    '请选择': 'Please select',
    '必填': 'Required',
    '可选': 'Optional',
    '格式错误': 'Format Error',
    '不能为空': 'Cannot be empty',
    
    // 操作结果
    '操作成功': 'Operation Successful',
    '操作失败': 'Operation Failed',
    '创建成功': 'Create Successful',
    '创建失败': 'Create Failed',
    '更新成功': 'Update Successful',
    '更新失败': 'Update Failed',
    '删除成功': 'Delete Successful',
    '删除失败': 'Delete Failed',
    '保存成功': 'Save Successful',
    '保存失败': 'Save Failed',
    
    // 其他常用
    '暂无数据': 'No Data',
    '加载失败': 'Load Failed',
    '网络异常': 'Network Error',
    '服务异常': 'Service Error',
    '权限不足': 'Insufficient Permission',
    '参数错误': 'Parameter Error',
    '未知错误': 'Unknown Error'
};

function batchTranslate(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 对每个翻译进行替换
        Object.entries(translations).forEach(([chinese, english]) => {
            // 使用正则表达式替换，确保只替换引号内的内容
            const regex = new RegExp(`'${chinese}'`, 'g');
            content = content.replace(regex, `'${english}'`);
        });
        
        // 写回文件
        fs.writeFileSync(filePath, content, 'utf8');
        console.log('批量翻译完成！');
        
    } catch (error) {
        console.error('翻译过程中出错：', error);
    }
}

// 使用方法：
// node batch-translate.js
// batchTranslate('src/locales/en-US.js');

module.exports = { batchTranslate, translations };
